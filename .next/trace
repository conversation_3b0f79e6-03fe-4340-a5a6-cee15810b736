[{"name":"hot-reloader","duration":97,"timestamp":81559507821,"id":3,"tags":{"version":"15.3.4"},"startTime":1751134330301,"traceId":"50dec7d6a1d857b0"},{"name":"setup-dev-bundler","duration":879362,"timestamp":81559124777,"id":2,"parentId":1,"tags":{},"startTime":1751134329918,"traceId":"50dec7d6a1d857b0"},{"name":"run-instrumentation-hook","duration":36,"timestamp":81560062682,"id":4,"parentId":1,"tags":{},"startTime":1751134330856,"traceId":"50dec7d6a1d857b0"},{"name":"start-dev-server","duration":1818966,"timestamp":81558262446,"id":1,"tags":{"cpus":"12","platform":"darwin","memory.freeMem":"32980992","memory.totalMem":"17179869184","memory.heapSizeLimit":"8640266240","memory.rss":"197931008","memory.heapTotal":"96391168","memory.heapUsed":"68173928"},"startTime":1751134329056,"traceId":"50dec7d6a1d857b0"},{"name":"compile-path","duration":4297417,"timestamp":81588128268,"id":7,"tags":{"trigger":"/"},"startTime":1751134358922,"traceId":"50dec7d6a1d857b0"},{"name":"ensure-page","duration":4300199,"timestamp":81588126238,"id":6,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1751134358920,"traceId":"50dec7d6a1d857b0"}]
[{"name":"ensure-page","duration":31366,"timestamp":81592432376,"id":8,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1751134363226,"traceId":"50dec7d6a1d857b0"},{"name":"handle-request","duration":4765347,"timestamp":81588114247,"id":5,"tags":{"url":"/"},"startTime":1751134358908,"traceId":"50dec7d6a1d857b0"},{"name":"memory-usage","duration":19,"timestamp":81592879907,"id":9,"parentId":5,"tags":{"url":"/","memory.rss":"506748928","memory.heapUsed":"81048288","memory.heapTotal":"98902016"},"startTime":1751134363674,"traceId":"50dec7d6a1d857b0"},{"name":"compile-path","duration":1077812,"timestamp":81593610392,"id":12,"tags":{"trigger":"/favicon.ico"},"startTime":1751134364404,"traceId":"50dec7d6a1d857b0"}]
[{"name":"ensure-page","duration":20323,"timestamp":81594691476,"id":13,"parentId":3,"tags":{"inputPage":"/favicon.ico/route"},"startTime":1751134365485,"traceId":"50dec7d6a1d857b0"},{"name":"handle-request","duration":1391381,"timestamp":81593597419,"id":10,"tags":{"url":"/favicon.ico?favicon.45db1c09.ico"},"startTime":1751134364391,"traceId":"50dec7d6a1d857b0"},{"name":"memory-usage","duration":9,"timestamp":81594988879,"id":14,"parentId":10,"tags":{"url":"/favicon.ico?favicon.45db1c09.ico","memory.rss":"583913472","memory.heapUsed":"88241008","memory.heapTotal":"103718912"},"startTime":1751134365783,"traceId":"50dec7d6a1d857b0"},{"name":"client-hmr-latency","duration":1082000,"timestamp":81605519352,"id":20,"parentId":3,"tags":{"updatedModules":[],"page":"/","isPageHidden":false},"startTime":1751134377419,"traceId":"50dec7d6a1d857b0"},{"name":"compile-path","duration":143989,"timestamp":81606495380,"id":17,"tags":{"trigger":"/"},"startTime":1751134377289,"traceId":"50dec7d6a1d857b0"}]
[{"name":"ensure-page","duration":147039,"timestamp":81606495147,"id":16,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1751134377289,"traceId":"50dec7d6a1d857b0"},{"name":"ensure-page","duration":23970,"timestamp":81606643324,"id":22,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1751134377437,"traceId":"50dec7d6a1d857b0"},{"name":"handle-request","duration":504721,"timestamp":81606491249,"id":15,"tags":{"url":"/?_rsc=1oqwi"},"startTime":1751134377285,"traceId":"50dec7d6a1d857b0"},{"name":"memory-usage","duration":8,"timestamp":81606996058,"id":25,"parentId":15,"tags":{"url":"/?_rsc=1oqwi","memory.rss":"579674112","memory.heapUsed":"98777352","memory.heapTotal":"115453952"},"startTime":1751134377790,"traceId":"50dec7d6a1d857b0"},{"name":"ensure-page","duration":359780,"timestamp":81606641536,"id":21,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1751134377436,"traceId":"50dec7d6a1d857b0"},{"name":"handle-request","duration":625371,"timestamp":81606525783,"id":18,"tags":{"url":"/"},"startTime":1751134377320,"traceId":"50dec7d6a1d857b0"},{"name":"memory-usage","duration":8,"timestamp":81607151213,"id":26,"parentId":18,"tags":{"url":"/","memory.rss":"590782464","memory.heapUsed":"102715776","memory.heapTotal":"136159232"},"startTime":1751134377945,"traceId":"50dec7d6a1d857b0"},{"name":"ensure-page","duration":196904,"timestamp":81606985283,"id":24,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1751134377779,"traceId":"50dec7d6a1d857b0"},{"name":"ensure-page","duration":33517,"timestamp":81607184684,"id":27,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1751134377979,"traceId":"50dec7d6a1d857b0"},{"name":"handle-request","duration":352999,"timestamp":81606983513,"id":23,"tags":{"url":"/"},"startTime":1751134377778,"traceId":"50dec7d6a1d857b0"},{"name":"memory-usage","duration":9,"timestamp":81607336658,"id":28,"parentId":23,"tags":{"url":"/","memory.rss":"599404544","memory.heapUsed":"111237168","memory.heapTotal":"136683520"},"startTime":1751134378131,"traceId":"50dec7d6a1d857b0"},{"name":"ensure-page","duration":37792,"timestamp":81607765572,"id":30,"parentId":3,"tags":{"inputPage":"/favicon.ico/route"},"startTime":1751134378560,"traceId":"50dec7d6a1d857b0"},{"name":"ensure-page","duration":23396,"timestamp":81607805623,"id":31,"parentId":3,"tags":{"inputPage":"/favicon.ico/route"},"startTime":1751134378600,"traceId":"50dec7d6a1d857b0"},{"name":"handle-request","duration":273579,"timestamp":81607763332,"id":29,"tags":{"url":"/favicon.ico?favicon.45db1c09.ico"},"startTime":1751134378557,"traceId":"50dec7d6a1d857b0"},{"name":"memory-usage","duration":6,"timestamp":81608036965,"id":32,"parentId":29,"tags":{"url":"/favicon.ico?favicon.45db1c09.ico","memory.rss":"594796544","memory.heapUsed":"104628080","memory.heapTotal":"136749056"},"startTime":1751134378831,"traceId":"50dec7d6a1d857b0"},{"name":"ensure-page","duration":28189,"timestamp":81608430352,"id":34,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1751134379224,"traceId":"50dec7d6a1d857b0"},{"name":"ensure-page","duration":20534,"timestamp":81608460053,"id":35,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1751134379254,"traceId":"50dec7d6a1d857b0"},{"name":"handle-request","duration":320734,"timestamp":81608429462,"id":33,"tags":{"url":"/"},"startTime":1751134379224,"traceId":"50dec7d6a1d857b0"},{"name":"memory-usage","duration":9,"timestamp":81608750261,"id":36,"parentId":33,"tags":{"url":"/","memory.rss":"599498752","memory.heapUsed":"112947480","memory.heapTotal":"136749056"},"startTime":1751134379544,"traceId":"50dec7d6a1d857b0"},{"name":"ensure-page","duration":36185,"timestamp":81608980097,"id":38,"parentId":3,"tags":{"inputPage":"/favicon.ico/route"},"startTime":1751134379774,"traceId":"50dec7d6a1d857b0"},{"name":"ensure-page","duration":21155,"timestamp":81609017453,"id":39,"parentId":3,"tags":{"inputPage":"/favicon.ico/route"},"startTime":1751134379812,"traceId":"50dec7d6a1d857b0"},{"name":"handle-request","duration":272997,"timestamp":81608978876,"id":37,"tags":{"url":"/favicon.ico?favicon.45db1c09.ico"},"startTime":1751134379773,"traceId":"50dec7d6a1d857b0"},{"name":"memory-usage","duration":6,"timestamp":81609251935,"id":40,"parentId":37,"tags":{"url":"/favicon.ico?favicon.45db1c09.ico","memory.rss":"567390208","memory.heapUsed":"104035096","memory.heapTotal":"138584064"},"startTime":1751134380046,"traceId":"50dec7d6a1d857b0"},{"name":"client-hmr-latency","duration":99000,"timestamp":81626141457,"id":41,"parentId":3,"tags":{"updatedModules":[],"page":"/","isPageHidden":true},"startTime":1751134397073,"traceId":"50dec7d6a1d857b0"},{"name":"client-hmr-latency","duration":210000,"timestamp":81674099294,"id":42,"parentId":3,"tags":{"updatedModules":[],"page":"/","isPageHidden":true},"startTime":1751134445109,"traceId":"50dec7d6a1d857b0"},{"name":"client-hmr-latency","duration":142000,"timestamp":81721563198,"id":43,"parentId":3,"tags":{"updatedModules":[],"page":"/","isPageHidden":true},"startTime":1751134492632,"traceId":"50dec7d6a1d857b0"},{"name":"ensure-page","duration":26512,"timestamp":81726215695,"id":45,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1751134497118,"traceId":"50dec7d6a1d857b0"},{"name":"ensure-page","duration":19954,"timestamp":81726244145,"id":46,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1751134497146,"traceId":"50dec7d6a1d857b0"},{"name":"handle-request","duration":127954,"timestamp":81726214504,"id":44,"tags":{"url":"/"},"startTime":1751134497117,"traceId":"50dec7d6a1d857b0"},{"name":"memory-usage","duration":6,"timestamp":81726342533,"id":47,"parentId":44,"tags":{"url":"/","memory.rss":"483282944","memory.heapUsed":"99785216","memory.heapTotal":"103456768"},"startTime":1751134497245,"traceId":"50dec7d6a1d857b0"},{"name":"ensure-page","duration":50555,"timestamp":81726980109,"id":49,"parentId":3,"tags":{"inputPage":"/favicon.ico/route"},"startTime":1751134497882,"traceId":"50dec7d6a1d857b0"},{"name":"ensure-page","duration":29511,"timestamp":81727032420,"id":50,"parentId":3,"tags":{"inputPage":"/favicon.ico/route"},"startTime":1751134497935,"traceId":"50dec7d6a1d857b0"},{"name":"handle-request","duration":288719,"timestamp":81726979032,"id":48,"tags":{"url":"/favicon.ico?favicon.45db1c09.ico"},"startTime":1751134497881,"traceId":"50dec7d6a1d857b0"},{"name":"memory-usage","duration":15,"timestamp":81727267943,"id":51,"parentId":48,"tags":{"url":"/favicon.ico?favicon.45db1c09.ico","memory.rss":"484519936","memory.heapUsed":"99569040","memory.heapTotal":"106602496"},"startTime":1751134498170,"traceId":"50dec7d6a1d857b0"},{"name":"client-hmr-latency","duration":140000,"timestamp":81770282534,"id":52,"parentId":3,"tags":{"updatedModules":[],"page":"/","isPageHidden":true},"startTime":1751134541355,"traceId":"50dec7d6a1d857b0"},{"name":"client-hmr-latency","duration":97000,"timestamp":81824666661,"id":53,"parentId":3,"tags":{"updatedModules":[],"page":"/","isPageHidden":true},"startTime":1751134595696,"traceId":"50dec7d6a1d857b0"},{"name":"client-hmr-latency","duration":138000,"timestamp":81878489840,"id":54,"parentId":3,"tags":{"updatedModules":[],"page":"/","isPageHidden":true},"startTime":1751134649559,"traceId":"50dec7d6a1d857b0"},{"name":"ensure-page","duration":29100,"timestamp":81955244313,"id":56,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1751134726153,"traceId":"50dec7d6a1d857b0"},{"name":"compile-path","duration":1958879,"timestamp":81955281569,"id":58,"tags":{"trigger":"/_error"},"startTime":1751134726190,"traceId":"50dec7d6a1d857b0"}]
[{"name":"handle-request","duration":2374506,"timestamp":81955243397,"id":55,"tags":{"url":"/"},"startTime":1751134726152,"traceId":"50dec7d6a1d857b0"},{"name":"memory-usage","duration":6,"timestamp":81957617990,"id":59,"parentId":55,"tags":{"url":"/","memory.rss":"685477888","memory.heapUsed":"108361376","memory.heapTotal":"119750656"},"startTime":1751134728527,"traceId":"50dec7d6a1d857b0"},{"name":"compile-path","duration":34446,"timestamp":81969846993,"id":62,"tags":{"trigger":"/"},"startTime":1751134740756,"traceId":"50dec7d6a1d857b0"}]
[{"name":"ensure-page","duration":32317,"timestamp":81969883750,"id":63,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1751134740793,"traceId":"50dec7d6a1d857b0"},{"name":"client-hmr-latency","duration":361000,"timestamp":81969543389,"id":64,"parentId":3,"tags":{"updatedModules":["[project]/src/components/About.tsx","[project]/src/components/Menu.tsx","[project]/src/components/Contact.tsx","[project]/src/components/Footer.tsx","[project]/node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js","[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js","[project]/node_modules/motion-utils/dist/es/warn-once.mjs","[project]/node_modules/framer-motion/dist/es/render/components/create-proxy.mjs","[project]/node_modules/framer-motion/dist/es/animation/utils/is-animation-controls.mjs","[project]/node_modules/framer-motion/dist/es/render/utils/resolve-variants.mjs","[project]/node_modules/framer-motion/dist/es/render/utils/resolve-dynamic-variants.mjs","[project]/node_modules/motion-dom/dist/es/animation/utils/get-value-transition.mjs","[project]/node_modules/motion-utils/dist/es/noop.mjs","[project]/node_modules/motion-utils/dist/es/global-config.mjs","[project]/node_modules/motion-dom/dist/es/frameloop/order.mjs","[project]/node_modules/motion-dom/dist/es/stats/buffer.mjs","[project]/node_modules/motion-dom/dist/es/frameloop/render-step.mjs","[project]/node_modules/motion-dom/dist/es/frameloop/batcher.mjs","[project]/node_modules/motion-dom/dist/es/frameloop/frame.mjs","[project]/node_modules/motion-dom/dist/es/render/utils/keys-transform.mjs","[project]/node_modules/motion-dom/dist/es/render/utils/keys-position.mjs","[project]/node_modules/motion-utils/dist/es/array.mjs","[project]/node_modules/motion-utils/dist/es/subscription-manager.mjs","[project]/node_modules/motion-utils/dist/es/velocity-per-second.mjs","[project]/node_modules/motion-dom/dist/es/frameloop/sync-time.mjs","[project]/node_modules/motion-dom/dist/es/value/index.mjs","[project]/node_modules/framer-motion/dist/es/animation/utils/is-keyframes-target.mjs","[project]/node_modules/framer-motion/dist/es/render/utils/setters.mjs","[project]/node_modules/motion-dom/dist/es/value/utils/is-motion-value.mjs","[project]/node_modules/framer-motion/dist/es/value/use-will-change/is.mjs","[project]/node_modules/framer-motion/dist/es/value/use-will-change/add-will-change.mjs","[project]/node_modules/framer-motion/dist/es/render/dom/utils/camel-to-dash.mjs","[project]/node_modules/framer-motion/dist/es/animation/optimized-appear/data-id.mjs","[project]/node_modules/framer-motion/dist/es/animation/optimized-appear/get-appear-id.mjs","[project]/node_modules/motion-utils/dist/es/errors.mjs","[project]/node_modules/motion-utils/dist/es/pipe.mjs","[project]/node_modules/motion-utils/dist/es/clamp.mjs","[project]/node_modules/motion-utils/dist/es/time-conversion.mjs","[project]/node_modules/motion-dom/dist/es/stats/animation-count.mjs","[project]/node_modules/motion-dom/dist/es/animation/utils/is-css-variable.mjs","[project]/node_modules/motion-dom/dist/es/value/types/numbers/index.mjs","[project]/node_modules/motion-dom/dist/es/value/types/utils/sanitize.mjs","[project]/node_modules/motion-dom/dist/es/value/types/utils/float-regex.mjs","[project]/node_modules/motion-dom/dist/es/value/types/utils/is-nullish.mjs","[project]/node_modules/motion-dom/dist/es/value/types/utils/single-color-regex.mjs","[project]/node_modules/motion-dom/dist/es/value/types/color/utils.mjs","[project]/node_modules/motion-dom/dist/es/value/types/color/rgba.mjs","[project]/node_modules/motion-dom/dist/es/value/types/color/hex.mjs","[project]/node_modules/motion-dom/dist/es/value/types/numbers/units.mjs","[project]/node_modules/motion-dom/dist/es/value/types/color/hsla.mjs","[project]/node_modules/motion-dom/dist/es/value/types/color/index.mjs","[project]/node_modules/motion-dom/dist/es/value/types/utils/color-regex.mjs","[project]/node_modules/motion-dom/dist/es/value/types/complex/index.mjs","[project]/node_modules/motion-dom/dist/es/value/types/color/hsla-to-rgba.mjs","[project]/node_modules/motion-dom/dist/es/utils/mix/immediate.mjs","[project]/node_modules/motion-dom/dist/es/utils/mix/number.mjs","[project]/node_modules/motion-dom/dist/es/utils/mix/color.mjs","[project]/node_modules/motion-dom/dist/es/utils/mix/visibility.mjs","[project]/node_modules/motion-dom/dist/es/utils/mix/complex.mjs","[project]/node_modules/motion-dom/dist/es/utils/mix/index.mjs","[project]/node_modules/motion-dom/dist/es/animation/drivers/frame.mjs","[project]/node_modules/motion-dom/dist/es/animation/waapi/utils/linear.mjs","[project]/node_modules/motion-dom/dist/es/animation/generators/utils/calc-duration.mjs","[project]/node_modules/motion-dom/dist/es/animation/generators/utils/create-generator-easing.mjs","[project]/node_modules/motion-dom/dist/es/animation/generators/utils/velocity.mjs","[project]/node_modules/motion-dom/dist/es/animation/generators/spring/defaults.mjs","[project]/node_modules/motion-dom/dist/es/animation/generators/spring/find.mjs","[project]/node_modules/motion-dom/dist/es/animation/generators/spring/index.mjs","[project]/node_modules/motion-dom/dist/es/animation/generators/inertia.mjs","[project]/node_modules/motion-utils/dist/es/easing/cubic-bezier.mjs","[project]/node_modules/motion-utils/dist/es/easing/ease.mjs","[project]/node_modules/motion-utils/dist/es/easing/utils/is-easing-array.mjs","[project]/node_modules/motion-utils/dist/es/easing/modifiers/mirror.mjs","[project]/node_modules/motion-utils/dist/es/easing/modifiers/reverse.mjs","[project]/node_modules/motion-utils/dist/es/easing/back.mjs","[project]/node_modules/motion-utils/dist/es/easing/anticipate.mjs","[project]/node_modules/motion-utils/dist/es/easing/circ.mjs","[project]/node_modules/motion-utils/dist/es/easing/utils/is-bezier-definition.mjs","[project]/node_modules/motion-utils/dist/es/easing/utils/map.mjs","[project]/node_modules/motion-utils/dist/es/progress.mjs","[project]/node_modules/motion-dom/dist/es/utils/interpolate.mjs","[project]/node_modules/motion-dom/dist/es/animation/keyframes/offsets/fill.mjs","[project]/node_modules/motion-dom/dist/es/animation/keyframes/offsets/default.mjs","[project]/node_modules/motion-dom/dist/es/animation/keyframes/offsets/time.mjs","[project]/node_modules/motion-dom/dist/es/animation/generators/keyframes.mjs","[project]/node_modules/motion-dom/dist/es/animation/keyframes/get-final.mjs","[project]/node_modules/motion-dom/dist/es/animation/utils/replace-transition-type.mjs","[project]/node_modules/motion-dom/dist/es/animation/utils/WithPromise.mjs","[project]/node_modules/motion-dom/dist/es/animation/JSAnimation.mjs","[project]/node_modules/motion-dom/dist/es/animation/keyframes/utils/fill-wildcards.mjs","[project]/node_modules/motion-dom/dist/es/render/dom/parse-transform.mjs","[project]/node_modules/motion-dom/dist/es/animation/keyframes/utils/unit-conversion.mjs","[project]/node_modules/motion-dom/dist/es/animation/keyframes/KeyframesResolver.mjs","[project]/node_modules/motion-dom/dist/es/render/dom/is-css-var.mjs","[project]/node_modules/motion-dom/dist/es/render/dom/style-set.mjs","[project]/node_modules/motion-utils/dist/es/memo.mjs","[project]/node_modules/motion-dom/dist/es/utils/supports/scroll-timeline.mjs","[project]/node_modules/motion-dom/dist/es/utils/supports/flags.mjs","[project]/node_modules/motion-dom/dist/es/utils/supports/memo.mjs","[project]/node_modules/motion-dom/dist/es/utils/supports/linear-easing.mjs","[project]/node_modules/motion-dom/dist/es/animation/waapi/easing/cubic-bezier.mjs","[project]/node_modules/motion-dom/dist/es/animation/waapi/easing/supported.mjs","[project]/node_modules/motion-dom/dist/es/animation/waapi/easing/map-easing.mjs","[project]/node_modules/motion-dom/dist/es/animation/waapi/start-waapi-animation.mjs","[project]/node_modules/motion-dom/dist/es/animation/generators/utils/is-generator.mjs","[project]/node_modules/motion-dom/dist/es/animation/waapi/utils/apply-generator.mjs","[project]/node_modules/motion-dom/dist/es/animation/NativeAnimation.mjs","[project]/node_modules/motion-dom/dist/es/animation/waapi/utils/unsupported-easing.mjs","[project]/node_modules/motion-dom/dist/es/animation/NativeAnimationExtended.mjs","[project]/node_modules/motion-dom/dist/es/animation/utils/is-animatable.mjs","[project]/node_modules/motion-dom/dist/es/animation/utils/can-animate.mjs","[project]/node_modules/motion-utils/dist/es/is-object.mjs","[project]/node_modules/motion-dom/dist/es/utils/is-html-element.mjs","[project]/node_modules/motion-dom/dist/es/animation/waapi/supports/waapi.mjs","[project]/node_modules/motion-dom/dist/es/animation/AsyncMotionValueAnimation.mjs","[project]/node_modules/framer-motion/dist/es/animation/animators/waapi/utils/get-final-keyframe.mjs","[project]/node_modules/framer-motion/dist/es/animation/utils/default-transitions.mjs","[project]/node_modules/framer-motion/dist/es/animation/utils/is-transition-defined.mjs","[project]/node_modules/framer-motion/dist/es/animation/interfaces/motion-value.mjs","[project]/node_modules/framer-motion/dist/es/animation/interfaces/visual-element-target.mjs","[project]/node_modules/framer-motion/dist/es/animation/interfaces/visual-element-variant.mjs","[project]/node_modules/framer-motion/dist/es/animation/interfaces/visual-element.mjs","[project]/node_modules/framer-motion/dist/es/utils/shallow-compare.mjs","[project]/node_modules/framer-motion/dist/es/render/utils/is-variant-label.mjs","[project]/node_modules/framer-motion/dist/es/render/utils/variant-props.mjs","[project]/node_modules/framer-motion/dist/es/render/utils/get-variant-context.mjs","[project]/node_modules/framer-motion/dist/es/render/utils/animation-state.mjs","[project]/node_modules/framer-motion/dist/es/motion/features/Feature.mjs","[project]/node_modules/framer-motion/dist/es/motion/features/animation/index.mjs","[project]/node_modules/framer-motion/dist/es/motion/features/animation/exit.mjs","[project]/node_modules/framer-motion/dist/es/motion/features/animations.mjs","[project]/node_modules/motion-dom/dist/es/gestures/drag/state/is-active.mjs","[project]/node_modules/motion-dom/dist/es/gestures/drag/state/set-active.mjs","[project]/node_modules/framer-motion/dist/es/events/add-dom-event.mjs","[project]/node_modules/motion-dom/dist/es/gestures/utils/is-primary-pointer.mjs","[project]/node_modules/framer-motion/dist/es/events/event-info.mjs","[project]/node_modules/framer-motion/dist/es/events/add-pointer-event.mjs","[project]/node_modules/framer-motion/dist/es/projection/geometry/conversion.mjs","[project]/node_modules/framer-motion/dist/es/projection/geometry/delta-calc.mjs","[project]/node_modules/framer-motion/dist/es/projection/geometry/models.mjs","[project]/node_modules/framer-motion/dist/es/projection/utils/each-axis.mjs","[project]/node_modules/framer-motion/dist/es/projection/utils/has-transform.mjs","[project]/node_modules/framer-motion/dist/es/projection/geometry/delta-apply.mjs","[project]/node_modules/framer-motion/dist/es/projection/utils/measure.mjs","[project]/node_modules/framer-motion/dist/es/utils/get-context-window.mjs","[project]/node_modules/framer-motion/dist/es/utils/is-ref-object.mjs","[project]/node_modules/framer-motion/dist/es/utils/distance.mjs","[project]/node_modules/framer-motion/dist/es/gestures/pan/PanSession.mjs","[project]/node_modules/framer-motion/dist/es/gestures/drag/utils/constraints.mjs","[project]/node_modules/framer-motion/dist/es/gestures/drag/VisualElementDragControls.mjs","[project]/node_modules/framer-motion/dist/es/gestures/drag/index.mjs","[project]/node_modules/framer-motion/dist/es/gestures/pan/index.mjs","[project]/node_modules/motion-dom/dist/es/frameloop/microtask.mjs","[project]/node_modules/framer-motion/dist/es/context/PresenceContext.mjs","[project]/node_modules/framer-motion/dist/es/components/AnimatePresence/use-presence.mjs","[project]/node_modules/framer-motion/dist/es/context/LayoutGroupContext.mjs","[project]/node_modules/framer-motion/dist/es/context/SwitchLayoutGroupContext.mjs","[project]/node_modules/framer-motion/dist/es/projection/node/state.mjs","[project]/node_modules/framer-motion/dist/es/projection/styles/scale-border-radius.mjs","[project]/node_modules/framer-motion/dist/es/projection/styles/scale-box-shadow.mjs","[project]/node_modules/framer-motion/dist/es/projection/styles/scale-correction.mjs","[project]/node_modules/framer-motion/dist/es/motion/features/layout/MeasureLayout.mjs","[project]/node_modules/motion-dom/dist/es/utils/is-svg-element.mjs","[project]/node_modules/motion-dom/dist/es/utils/is-svg-svg-element.mjs","[project]/node_modules/framer-motion/dist/es/animation/animate/single-value.mjs","[project]/node_modules/framer-motion/dist/es/render/utils/compare-by-depth.mjs","[project]/node_modules/framer-motion/dist/es/render/utils/flat-tree.mjs","[project]/node_modules/framer-motion/dist/es/utils/delay.mjs","[project]/node_modules/framer-motion/dist/es/value/utils/resolve-motion-value.mjs","[project]/node_modules/framer-motion/dist/es/projection/animation/mix-values.mjs","[project]/node_modules/framer-motion/dist/es/projection/geometry/copy.mjs","[project]/node_modules/framer-motion/dist/es/projection/geometry/delta-remove.mjs","[project]/node_modules/framer-motion/dist/es/projection/geometry/utils.mjs","[project]/node_modules/framer-motion/dist/es/projection/shared/stack.mjs","[project]/node_modules/framer-motion/dist/es/projection/styles/transform.mjs","[project]/node_modules/framer-motion/dist/es/projection/node/create-projection-node.mjs","[project]/node_modules/framer-motion/dist/es/projection/node/DocumentProjectionNode.mjs","[project]/node_modules/framer-motion/dist/es/projection/node/HTMLProjectionNode.mjs","[project]/node_modules/framer-motion/dist/es/motion/features/drag.mjs","[project]/node_modules/motion-dom/dist/es/utils/resolve-elements.mjs","[project]/node_modules/motion-dom/dist/es/gestures/utils/setup.mjs","[project]/node_modules/motion-dom/dist/es/gestures/hover.mjs","[project]/node_modules/framer-motion/dist/es/gestures/hover.mjs","[project]/node_modules/framer-motion/dist/es/gestures/focus.mjs","[project]/node_modules/motion-dom/dist/es/gestures/utils/is-node-or-child.mjs","[project]/node_modules/motion-dom/dist/es/gestures/press/utils/is-keyboard-accessible.mjs","[project]/node_modules/motion-dom/dist/es/gestures/press/utils/state.mjs","[project]/node_modules/motion-dom/dist/es/gestures/press/utils/keyboard.mjs","[project]/node_modules/motion-dom/dist/es/gestures/press/index.mjs","[project]/node_modules/framer-motion/dist/es/gestures/press.mjs","[project]/node_modules/framer-motion/dist/es/motion/features/viewport/observers.mjs","[project]/node_modules/framer-motion/dist/es/motion/features/viewport/index.mjs","[project]/node_modules/framer-motion/dist/es/motion/features/gestures.mjs","[project]/node_modules/framer-motion/dist/es/motion/features/layout.mjs","[project]/node_modules/framer-motion/dist/es/context/LazyContext.mjs","[project]/node_modules/framer-motion/dist/es/context/MotionConfigContext.mjs","[project]/node_modules/framer-motion/dist/es/context/MotionContext/index.mjs","[project]/node_modules/framer-motion/dist/es/render/utils/is-controlling-variants.mjs","[project]/node_modules/framer-motion/dist/es/context/MotionContext/utils.mjs","[project]/node_modules/framer-motion/dist/es/context/MotionContext/create.mjs","[project]/node_modules/framer-motion/dist/es/utils/is-browser.mjs","[project]/node_modules/framer-motion/dist/es/motion/features/definitions.mjs","[project]/node_modules/framer-motion/dist/es/motion/features/load-features.mjs","[project]/node_modules/framer-motion/dist/es/motion/utils/symbol.mjs","[project]/node_modules/framer-motion/dist/es/motion/utils/use-motion-ref.mjs","[project]/node_modules/framer-motion/dist/es/utils/use-isomorphic-effect.mjs","[project]/node_modules/framer-motion/dist/es/motion/utils/use-visual-element.mjs","[project]/node_modules/framer-motion/dist/es/motion/index.mjs","[project]/node_modules/framer-motion/dist/es/motion/utils/is-forced-motion-value.mjs","[project]/node_modules/motion-dom/dist/es/value/types/utils/get-as-type.mjs","[project]/node_modules/motion-dom/dist/es/value/types/int.mjs","[project]/node_modules/motion-dom/dist/es/value/types/maps/transform.mjs","[project]/node_modules/motion-dom/dist/es/value/types/maps/number.mjs","[project]/node_modules/framer-motion/dist/es/render/html/utils/build-transform.mjs","[project]/node_modules/framer-motion/dist/es/render/html/utils/build-styles.mjs","[project]/node_modules/framer-motion/dist/es/render/html/utils/create-render-state.mjs","[project]/node_modules/framer-motion/dist/es/render/html/use-props.mjs","[project]/node_modules/framer-motion/dist/es/render/svg/utils/path.mjs","[project]/node_modules/framer-motion/dist/es/render/svg/utils/build-attrs.mjs","[project]/node_modules/framer-motion/dist/es/render/svg/utils/create-render-state.mjs","[project]/node_modules/framer-motion/dist/es/render/svg/utils/is-svg-tag.mjs","[project]/node_modules/framer-motion/dist/es/render/svg/use-props.mjs","[project]/node_modules/framer-motion/dist/es/motion/utils/valid-prop.mjs","[project]/node_modules/framer-motion/dist/es/render/dom/utils/filter-props.mjs","[project]/node_modules/framer-motion/dist/es/render/svg/lowercase-elements.mjs","[project]/node_modules/framer-motion/dist/es/render/dom/utils/is-svg-component.mjs","[project]/node_modules/framer-motion/dist/es/render/dom/use-render.mjs","[project]/node_modules/framer-motion/dist/es/utils/use-constant.mjs","[project]/node_modules/framer-motion/dist/es/motion/utils/use-visual-state.mjs","[project]/node_modules/framer-motion/dist/es/render/html/utils/scrape-motion-values.mjs","[project]/node_modules/framer-motion/dist/es/render/html/config-motion.mjs","[project]/node_modules/framer-motion/dist/es/render/svg/utils/scrape-motion-values.mjs","[project]/node_modules/framer-motion/dist/es/render/svg/config-motion.mjs","[project]/node_modules/framer-motion/dist/es/render/components/create-factory.mjs","[project]/node_modules/motion-dom/dist/es/value/types/auto.mjs","[project]/node_modules/motion-dom/dist/es/value/types/test.mjs","[project]/node_modules/motion-dom/dist/es/value/types/dimensions.mjs","[project]/node_modules/motion-utils/dist/es/is-numerical-string.mjs","[project]/node_modules/motion-dom/dist/es/animation/utils/css-variables-conversion.mjs","[project]/node_modules/motion-utils/dist/es/is-zero-value-string.mjs","[project]/node_modules/motion-dom/dist/es/animation/keyframes/utils/is-none.mjs","[project]/node_modules/motion-dom/dist/es/value/types/complex/filter.mjs","[project]/node_modules/motion-dom/dist/es/value/types/maps/defaults.mjs","[project]/node_modules/motion-dom/dist/es/value/types/utils/animatable-none.mjs","[project]/node_modules/motion-dom/dist/es/animation/keyframes/utils/make-none-animatable.mjs","[project]/node_modules/motion-dom/dist/es/animation/keyframes/DOMKeyframesResolver.mjs","[project]/node_modules/motion-dom/dist/es/value/types/utils/find.mjs","[project]/node_modules/framer-motion/dist/es/utils/reduced-motion/state.mjs","[project]/node_modules/framer-motion/dist/es/utils/reduced-motion/index.mjs","[project]/node_modules/framer-motion/dist/es/render/store.mjs","[project]/node_modules/framer-motion/dist/es/render/utils/motion-values.mjs","[project]/node_modules/framer-motion/dist/es/render/VisualElement.mjs","[project]/node_modules/framer-motion/dist/es/render/dom/DOMVisualElement.mjs","[project]/node_modules/framer-motion/dist/es/render/html/utils/render.mjs","[project]/node_modules/framer-motion/dist/es/render/html/HTMLVisualElement.mjs","[project]/node_modules/framer-motion/dist/es/render/svg/utils/camel-case-attrs.mjs","[project]/node_modules/framer-motion/dist/es/render/svg/utils/render.mjs","[project]/node_modules/framer-motion/dist/es/render/svg/SVGVisualElement.mjs","[project]/node_modules/framer-motion/dist/es/render/dom/create-visual-element.mjs","[project]/node_modules/framer-motion/dist/es/render/components/motion/create.mjs","[project]/node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs","[project]/node_modules/framer-motion/dist/es/render/dom/viewport/index.mjs","[project]/node_modules/framer-motion/dist/es/utils/use-in-view.mjs","[project]/src/components/Hero.tsx"],"page":"/","isPageHidden":true},"startTime":1751134741197,"traceId":"50dec7d6a1d857b0"},{"name":"handle-request","duration":445720,"timestamp":81969844850,"id":60,"tags":{"url":"/?_rsc=1oqwi"},"startTime":1751134740754,"traceId":"50dec7d6a1d857b0"},{"name":"memory-usage","duration":5,"timestamp":81970290654,"id":65,"parentId":60,"tags":{"url":"/?_rsc=1oqwi","memory.rss":"704806912","memory.heapUsed":"119480736","memory.heapTotal":"146309120"},"startTime":1751134741200,"traceId":"50dec7d6a1d857b0"},{"name":"compile-path","duration":33913,"timestamp":82076333303,"id":68,"tags":{"trigger":"/"},"startTime":1751134847246,"traceId":"50dec7d6a1d857b0"}]
[{"name":"client-hmr-latency","duration":269000,"timestamp":82076114866,"id":70,"parentId":3,"tags":{"updatedModules":[],"page":"/","isPageHidden":true},"startTime":1751134847297,"traceId":"50dec7d6a1d857b0"},{"name":"ensure-page","duration":19701,"timestamp":82076370601,"id":69,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1751134847283,"traceId":"50dec7d6a1d857b0"},{"name":"handle-request","duration":361287,"timestamp":82076331349,"id":66,"tags":{"url":"/?_rsc=1oqwi"},"startTime":1751134847244,"traceId":"50dec7d6a1d857b0"},{"name":"memory-usage","duration":8,"timestamp":82076692957,"id":71,"parentId":66,"tags":{"url":"/?_rsc=1oqwi","memory.rss":"725520384","memory.heapUsed":"132245872","memory.heapTotal":"142458880"},"startTime":1751134847605,"traceId":"50dec7d6a1d857b0"},{"name":"client-hmr-latency","duration":183000,"timestamp":82105757532,"id":75,"parentId":3,"tags":{"updatedModules":["[project]/src/components/Hero.tsx"],"page":"/","isPageHidden":true},"startTime":1751134876887,"traceId":"50dec7d6a1d857b0"},{"name":"compile-path","duration":43025,"timestamp":82105946876,"id":74,"tags":{"trigger":"/"},"startTime":1751134876853,"traceId":"50dec7d6a1d857b0"}]
[{"name":"hot-reloader","duration":108,"timestamp":82188027979,"id":3,"tags":{"version":"15.3.4"},"startTime":1751134958930,"traceId":"203f94e8601a5a62"},{"name":"setup-dev-bundler","duration":1344650,"timestamp":82187423199,"id":2,"parentId":1,"tags":{},"startTime":1751134958325,"traceId":"203f94e8601a5a62"},{"name":"run-instrumentation-hook","duration":39,"timestamp":82188829048,"id":4,"parentId":1,"tags":{},"startTime":1751134959731,"traceId":"203f94e8601a5a62"},{"name":"start-dev-server","duration":2270025,"timestamp":82186579736,"id":1,"tags":{"cpus":"12","platform":"darwin","memory.freeMem":"663523328","memory.totalMem":"17179869184","memory.heapSizeLimit":"8640266240","memory.rss":"200400896","memory.heapTotal":"96391168","memory.heapUsed":"67543824"},"startTime":1751134957482,"traceId":"203f94e8601a5a62"},{"name":"compile-path","duration":3596601,"timestamp":82191455539,"id":7,"tags":{"trigger":"/"},"startTime":1751134962358,"traceId":"203f94e8601a5a62"},{"name":"ensure-page","duration":3598311,"timestamp":82191454599,"id":6,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1751134962357,"traceId":"203f94e8601a5a62"}]
[{"name":"ensure-page","duration":17655,"timestamp":82195057105,"id":8,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1751134965959,"traceId":"203f94e8601a5a62"},{"name":"handle-request","duration":4325026,"timestamp":82191442079,"id":5,"tags":{"url":"/"},"startTime":1751134962344,"traceId":"203f94e8601a5a62"},{"name":"memory-usage","duration":9,"timestamp":82195767215,"id":9,"parentId":5,"tags":{"url":"/","memory.rss":"619704320","memory.heapUsed":"84311088","memory.heapTotal":"120979456"},"startTime":1751134966669,"traceId":"203f94e8601a5a62"},{"name":"client-hmr-latency","duration":148000,"timestamp":82308907241,"id":10,"parentId":3,"tags":{"updatedModules":["[project]/src/components/Hero.tsx"],"page":"/","isPageHidden":true},"startTime":1751135079990,"traceId":"203f94e8601a5a62"},{"name":"ensure-page","duration":36604,"timestamp":82311511316,"id":12,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1751135082417,"traceId":"203f94e8601a5a62"},{"name":"ensure-page","duration":22390,"timestamp":82311551842,"id":13,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1751135082457,"traceId":"203f94e8601a5a62"},{"name":"handle-request","duration":630190,"timestamp":82311509864,"id":11,"tags":{"url":"/"},"startTime":1751135082415,"traceId":"203f94e8601a5a62"},{"name":"memory-usage","duration":9,"timestamp":82312140186,"id":14,"parentId":11,"tags":{"url":"/","memory.rss":"507072512","memory.heapUsed":"107203344","memory.heapTotal":"123740160"},"startTime":1751135083046,"traceId":"203f94e8601a5a62"},{"name":"compile-path","duration":1092650,"timestamp":82312714215,"id":17,"tags":{"trigger":"/favicon.ico"},"startTime":1751135083620,"traceId":"203f94e8601a5a62"}]
[{"name":"ensure-page","duration":15417,"timestamp":82313809048,"id":18,"parentId":3,"tags":{"inputPage":"/favicon.ico/route"},"startTime":1751135084715,"traceId":"203f94e8601a5a62"},{"name":"handle-request","duration":1395004,"timestamp":82312711503,"id":15,"tags":{"url":"/favicon.ico?favicon.45db1c09.ico"},"startTime":1751135083617,"traceId":"203f94e8601a5a62"},{"name":"memory-usage","duration":6,"timestamp":82314106573,"id":19,"parentId":15,"tags":{"url":"/favicon.ico?favicon.45db1c09.ico","memory.rss":"567353344","memory.heapUsed":"113769440","memory.heapTotal":"129269760"},"startTime":1751135085012,"traceId":"203f94e8601a5a62"},{"name":"client-hmr-latency","duration":117000,"timestamp":82334619315,"id":20,"parentId":3,"tags":{"updatedModules":["[project]/src/components/Hero.tsx"],"page":"/","isPageHidden":true},"startTime":1751135105671,"traceId":"203f94e8601a5a62"},{"name":"client-hmr-latency","duration":151000,"timestamp":82784016886,"id":21,"parentId":3,"tags":{"updatedModules":["[project]/src/components/Hero.tsx"],"page":"/","isPageHidden":true},"startTime":1751135555163,"traceId":"203f94e8601a5a62"},{"name":"client-hmr-latency","duration":146000,"timestamp":82802020012,"id":22,"parentId":3,"tags":{"updatedModules":["[project]/src/components/Hero.tsx"],"page":"/","isPageHidden":true},"startTime":1751135573164,"traceId":"203f94e8601a5a62"},{"name":"ensure-page","duration":39433,"timestamp":82804619886,"id":24,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1751135575589,"traceId":"203f94e8601a5a62"},{"name":"ensure-page","duration":21467,"timestamp":82804661580,"id":25,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1751135575631,"traceId":"203f94e8601a5a62"},{"name":"handle-request","duration":583652,"timestamp":82804617519,"id":23,"tags":{"url":"/"},"startTime":1751135575586,"traceId":"203f94e8601a5a62"},{"name":"memory-usage","duration":8,"timestamp":82805201258,"id":26,"parentId":23,"tags":{"url":"/","memory.rss":"516894720","memory.heapUsed":"122626824","memory.heapTotal":"142344192"},"startTime":1751135576170,"traceId":"203f94e8601a5a62"},{"name":"ensure-page","duration":47192,"timestamp":82805800906,"id":28,"parentId":3,"tags":{"inputPage":"/favicon.ico/route"},"startTime":1751135576770,"traceId":"203f94e8601a5a62"},{"name":"ensure-page","duration":17322,"timestamp":82805850130,"id":29,"parentId":3,"tags":{"inputPage":"/favicon.ico/route"},"startTime":1751135576819,"traceId":"203f94e8601a5a62"},{"name":"handle-request","duration":278029,"timestamp":82805796754,"id":27,"tags":{"url":"/favicon.ico?favicon.45db1c09.ico"},"startTime":1751135576766,"traceId":"203f94e8601a5a62"},{"name":"memory-usage","duration":11,"timestamp":82806075099,"id":30,"parentId":27,"tags":{"url":"/favicon.ico?favicon.45db1c09.ico","memory.rss":"519426048","memory.heapUsed":"127697920","memory.heapTotal":"142606336"},"startTime":1751135577044,"traceId":"203f94e8601a5a62"},{"name":"ensure-page","duration":1358,"timestamp":82815239481,"id":31,"parentId":3,"tags":{"inputPage":"/.well-known/appspecific/com.chrome.devtools.json"},"startTime":1751135586209,"traceId":"203f94e8601a5a62"},{"name":"ensure-page","duration":1356,"timestamp":82815240924,"id":32,"parentId":3,"tags":{"inputPage":"/.well-known/appspecific/com.chrome.devtools.json"},"startTime":1751135586210,"traceId":"203f94e8601a5a62"},{"name":"ensure-page","duration":4578,"timestamp":82815250919,"id":33,"parentId":3,"tags":{"inputPage":"/.well-known/appspecific/com.chrome.devtools.json"},"startTime":1751135586220,"traceId":"203f94e8601a5a62"},{"name":"ensure-page","duration":8157,"timestamp":82815255807,"id":34,"parentId":3,"tags":{"inputPage":"/.well-known/appspecific/com.chrome.devtools.json"},"startTime":1751135586225,"traceId":"203f94e8601a5a62"},{"name":"compile-path","duration":1086111,"timestamp":82815269770,"id":37,"tags":{"trigger":"/_not-found/page"},"startTime":1751135586239,"traceId":"203f94e8601a5a62"}]
[{"name":"next-dev","duration":725472637,"timestamp":82185434196,"id":1,"tags":{},"startTime":1751134956336,"traceId":"203f94e8601a5a62"}]
[{"name":"hot-reloader","duration":100,"timestamp":82887555572,"id":3,"tags":{"version":"15.3.4"},"startTime":1751135658527,"traceId":"b0a7889ef55ce76f"},{"name":"setup-dev-bundler","duration":822618,"timestamp":82887189213,"id":2,"parentId":1,"tags":{},"startTime":1751135658161,"traceId":"b0a7889ef55ce76f"},{"name":"run-instrumentation-hook","duration":38,"timestamp":82888074097,"id":4,"parentId":1,"tags":{},"startTime":1751135659046,"traceId":"b0a7889ef55ce76f"},{"name":"start-dev-server","duration":1635700,"timestamp":82886460369,"id":1,"tags":{"cpus":"12","platform":"darwin","memory.freeMem":"20275200","memory.totalMem":"17179869184","memory.heapSizeLimit":"8640266240","memory.rss":"195248128","memory.heapTotal":"96391168","memory.heapUsed":"67499584"},"startTime":1751135657432,"traceId":"b0a7889ef55ce76f"},{"name":"compile-path","duration":3361974,"timestamp":82911405419,"id":7,"tags":{"trigger":"/"},"startTime":1751135682378,"traceId":"b0a7889ef55ce76f"},{"name":"ensure-page","duration":3365099,"timestamp":82911404455,"id":6,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1751135682377,"traceId":"b0a7889ef55ce76f"}]
[{"name":"hot-reloader","duration":3557,"timestamp":83293127197,"id":3,"tags":{"version":"15.3.4"},"startTime":1751136064101,"traceId":"75c72182fb5d6ed6"},{"name":"setup-dev-bundler","duration":1216949,"timestamp":83292541673,"id":2,"parentId":1,"tags":{},"startTime":1751136063514,"traceId":"75c72182fb5d6ed6"},{"name":"run-instrumentation-hook","duration":39,"timestamp":83293818241,"id":4,"parentId":1,"tags":{},"startTime":1751136064791,"traceId":"75c72182fb5d6ed6"},{"name":"start-dev-server","duration":2048885,"timestamp":83291791068,"id":1,"tags":{"cpus":"12","platform":"darwin","memory.freeMem":"22077440","memory.totalMem":"17179869184","memory.heapSizeLimit":"8640266240","memory.rss":"197173248","memory.heapTotal":"96915456","memory.heapUsed":"67277368"},"startTime":1751136062764,"traceId":"75c72182fb5d6ed6"},{"name":"compile-path","duration":4265468,"timestamp":83294600731,"id":7,"tags":{"trigger":"/"},"startTime":1751136065573,"traceId":"75c72182fb5d6ed6"},{"name":"ensure-page","duration":4266986,"timestamp":83294600239,"id":6,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1751136065573,"traceId":"75c72182fb5d6ed6"}]
[{"name":"ensure-page","duration":27901,"timestamp":83298873829,"id":8,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1751136069846,"traceId":"75c72182fb5d6ed6"},{"name":"handle-request","duration":4962179,"timestamp":83294592977,"id":5,"tags":{"url":"/"},"startTime":1751136065565,"traceId":"75c72182fb5d6ed6"},{"name":"memory-usage","duration":9,"timestamp":83299555268,"id":9,"parentId":5,"tags":{"url":"/","memory.rss":"608129024","memory.heapUsed":"83980856","memory.heapTotal":"121499648"},"startTime":1751136070528,"traceId":"75c72182fb5d6ed6"},{"name":"compile-path","duration":196456,"timestamp":260266971522,"id":12,"tags":{"trigger":"/"},"startTime":1751313039226,"traceId":"75c72182fb5d6ed6"}]
[{"name":"ensure-page","duration":56831,"timestamp":260267178697,"id":13,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1751313039433,"traceId":"75c72182fb5d6ed6"},{"name":"handle-request","duration":1002978,"timestamp":260266926949,"id":10,"tags":{"url":"/"},"startTime":1751313039181,"traceId":"75c72182fb5d6ed6"},{"name":"memory-usage","duration":10,"timestamp":260267930621,"id":14,"parentId":10,"tags":{"url":"/","memory.rss":"154800128","memory.heapUsed":"110755520","memory.heapTotal":"124846080"},"startTime":1751313040185,"traceId":"75c72182fb5d6ed6"},{"name":"client-hmr-latency","duration":514000,"timestamp":260270482297,"id":15,"parentId":3,"tags":{"updatedModules":[],"page":"/","isPageHidden":false},"startTime":1751313043293,"traceId":"75c72182fb5d6ed6"},{"name":"ensure-page","duration":19143,"timestamp":260274460583,"id":17,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1751313046715,"traceId":"75c72182fb5d6ed6"},{"name":"ensure-page","duration":17722,"timestamp":260274481369,"id":18,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1751313046735,"traceId":"75c72182fb5d6ed6"},{"name":"handle-request","duration":184940,"timestamp":260274458160,"id":16,"tags":{"url":"/"},"startTime":1751313046712,"traceId":"75c72182fb5d6ed6"},{"name":"memory-usage","duration":8,"timestamp":260274643257,"id":19,"parentId":16,"tags":{"url":"/","memory.rss":"176582656","memory.heapUsed":"112259368","memory.heapTotal":"143343616"},"startTime":1751313046897,"traceId":"75c72182fb5d6ed6"},{"name":"compile-path","duration":1286642,"timestamp":260274954849,"id":22,"tags":{"trigger":"/favicon.ico"},"startTime":1751313047209,"traceId":"75c72182fb5d6ed6"}]
[{"name":"ensure-page","duration":22712,"timestamp":260276243990,"id":23,"parentId":3,"tags":{"inputPage":"/favicon.ico/route"},"startTime":1751313048498,"traceId":"75c72182fb5d6ed6"},{"name":"handle-request","duration":1627559,"timestamp":260274950526,"id":20,"tags":{"url":"/favicon.ico?favicon.45db1c09.ico"},"startTime":1751313047205,"traceId":"75c72182fb5d6ed6"},{"name":"memory-usage","duration":6,"timestamp":260276578166,"id":24,"parentId":20,"tags":{"url":"/favicon.ico?favicon.45db1c09.ico","memory.rss":"373268480","memory.heapUsed":"104311320","memory.heapTotal":"110239744"},"startTime":1751313048832,"traceId":"75c72182fb5d6ed6"},{"name":"ensure-page","duration":5589,"timestamp":260289178927,"id":25,"parentId":3,"tags":{"inputPage":"/.well-known/appspecific/com.chrome.devtools.json"},"startTime":1751313061433,"traceId":"75c72182fb5d6ed6"},{"name":"ensure-page","duration":178,"timestamp":260289185225,"id":26,"parentId":3,"tags":{"inputPage":"/.well-known/appspecific/com.chrome.devtools.json"},"startTime":1751313061439,"traceId":"75c72182fb5d6ed6"},{"name":"ensure-page","duration":144,"timestamp":260289186203,"id":27,"parentId":3,"tags":{"inputPage":"/.well-known/appspecific/com.chrome.devtools.json"},"startTime":1751313061440,"traceId":"75c72182fb5d6ed6"},{"name":"ensure-page","duration":187,"timestamp":260289186386,"id":28,"parentId":3,"tags":{"inputPage":"/.well-known/appspecific/com.chrome.devtools.json"},"startTime":1751313061440,"traceId":"75c72182fb5d6ed6"},{"name":"compile-path","duration":1564044,"timestamp":260289191343,"id":31,"tags":{"trigger":"/_not-found/page"},"startTime":1751313061445,"traceId":"75c72182fb5d6ed6"}]
[{"name":"next-dev","duration":177087285346,"timestamp":83290418020,"id":1,"tags":{},"startTime":1751136061390,"traceId":"75c72182fb5d6ed6"}]
