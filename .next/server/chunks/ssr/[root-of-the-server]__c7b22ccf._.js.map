{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/ibm_plex_sans_arabic_c9dc9ba1.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"ibm_plex_sans_arabic_c9dc9ba1-module__ICuBsa__className\",\n  \"variable\": \"ibm_plex_sans_arabic_c9dc9ba1-module__ICuBsa__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 16, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/ibm_plex_sans_arabic_c9dc9ba1.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22IBM_Plex_Sans_Arabic%22,%22arguments%22:[{%22variable%22:%22--font-ibm-plex-sans-arabic%22,%22subsets%22:[%22arabic%22],%22weight%22:[%22100%22,%22200%22,%22300%22,%22400%22,%22500%22,%22600%22,%22700%22],%22display%22:%22swap%22}],%22variableName%22:%22ibmPlexSansArabic%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'IBM Plex Sans Arabic', 'IBM Plex Sans Arabic Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,oKAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,oKAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,oKAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 38, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/hiwa_madha_kababchy/src/app/layout.tsx"], "sourcesContent": ["import type { Metadata } from \"next\";\nimport { IBM_Plex_Sans_Arabic } from \"next/font/google\";\nimport \"./globals.css\";\n\nconst ibmPlexSansArabic = IBM_Plex_Sans_Arabic({\n  variable: \"--font-ibm-plex-sans-arabic\",\n  subsets: [\"arabic\"],\n  weight: [\"100\", \"200\", \"300\", \"400\", \"500\", \"600\", \"700\"],\n  display: \"swap\",\n});\n\nexport const metadata: Metadata = {\n  title: \"هیوا مەدا کەبابچی - Hiwa Madha <PERSON>ba<PERSON>chy\",\n  description:\n    \"چێشتخانەی کەبابی کوردی لە هەولێر - Kurdish Kabab Restaurant in Erbil\",\n};\n\nexport default function RootLayout({\n  children,\n}: Readonly<{\n  children: React.ReactNode;\n}>) {\n  return (\n    <html lang=\"ckb\" dir=\"rtl\">\n      <body className={`${ibmPlexSansArabic.variable} antialiased font-sans`}>\n        {children}\n      </body>\n    </html>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;;;AAWO,MAAM,WAAqB;IAChC,OAAO;IACP,aACE;AACJ;AAEe,SAAS,WAAW,EACjC,QAAQ,EAGR;IACA,qBACE,8OAAC;QAAK,MAAK;QAAM,KAAI;kBACnB,cAAA,8OAAC;YAAK,WAAW,GAAG,wJAAA,CAAA,UAAiB,CAAC,QAAQ,CAAC,sBAAsB,CAAC;sBACnE;;;;;;;;;;;AAIT", "debugId": null}}, {"offset": {"line": 74, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/augment-projects/hiwa_madha_kababchy/node_modules/next/src/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-rsc'\n].ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CACxD,YACD,CAACC,kBAAkB", "ignoreList": [0], "debugId": null}}]}