"use client";

import { motion } from "framer-motion";

const Hero = () => {
  return (
    <section
      className="relative min-h-screen flex flex-col items-center justify-center py-20"
      style={{ backgroundColor: "#EEECE8" }}
    >
      {/* Portrait Photo */}
      <motion.div
        initial={{ opacity: 0, scale: 0.8 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.8, ease: "easeOut" }}
        className="mb-8"
      >
        <div className="w-48 h-48 mx-auto rounded-lg overflow-hidden shadow-2xl border-4 border-white transform rotate-3">
          <img
            src="/portrait.jpg"
            alt="هیوا مدحت کەبابچی"
            className="w-full h-full object-cover"
            onError={(e) => {
              // Fallback if image doesn't exist
              e.currentTarget.style.display = "none";
              e.currentTarget.parentElement!.innerHTML =
                '<div class="w-full h-full bg-gray-300 flex items-center justify-center text-6xl">👨‍🍳</div>';
            }}
          />
        </div>
      </motion.div>

      {/* Restaurant Logo/Name */}
      <motion.div
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8, delay: 0.2, ease: "easeOut" }}
        className="text-center mb-8"
      >
        <div className="relative">
          <h1 className="text-4xl md:text-5xl font-bold text-gray-800 kurdish-text mb-2">
            هیوا مدحت کەبابچی
          </h1>
          <div className="text-lg text-gray-600 kurdish-text">١٩٣٦</div>
        </div>
      </motion.div>

      {/* Story Text */}
      <motion.div
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8, delay: 0.4, ease: "easeOut" }}
        className="max-w-2xl mx-auto text-center mb-12 px-6"
      >
        <p className="text-lg leading-relaxed text-gray-700 kurdish-text">
          هیوا مدحت کەبابچی لە ساڵی ١٩٣٦ ەوە دامەزراوە لە سەر دەستی وەستا مدحت ی
          باوکی وەستا هیوا و لە ساڵی ٢٠٢١ لە لایەن کوڕەکانی وەستا هیوا و وەستا
          هیوا کراوە بە یەکەمین بڕاندی کەباب لە کوردستان و عێراق
        </p>
      </motion.div>

      {/* Decorative Star */}
      <motion.div
        initial={{ opacity: 0, rotate: 0 }}
        animate={{ opacity: 1, rotate: 360 }}
        transition={{ duration: 2, delay: 0.6, ease: "easeOut" }}
        className="mb-8"
      >
        <div className="text-4xl text-isolator-red">✦</div>
      </motion.div>

      {/* Call to Action */}
      <motion.div
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8, delay: 0.8, ease: "easeOut" }}
        className="flex flex-col sm:flex-row gap-4 justify-center items-center"
      >
        <motion.button
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          className="bg-isolator-red text-white px-8 py-3 rounded-lg text-lg font-semibold shadow-lg hover:shadow-xl transition-all duration-300 kurdish-text"
          onClick={() =>
            document
              .getElementById("menu")
              ?.scrollIntoView({ behavior: "smooth" })
          }
        >
          بینینی مینو
        </motion.button>

        <motion.button
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          className="bg-isolator-blue text-white px-8 py-3 rounded-lg text-lg font-semibold shadow-lg hover:shadow-xl transition-all duration-300 kurdish-text"
          onClick={() =>
            document
              .getElementById("contact")
              ?.scrollIntoView({ behavior: "smooth" })
          }
        >
          پەیوەندی
        </motion.button>
      </motion.div>
    </section>
  );
};

export default Hero;
