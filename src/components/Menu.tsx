"use client";

import { motion } from "framer-motion";
import { useInView } from "framer-motion";
import { useRef } from "react";

const Menu = () => {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: "-100px" });

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: "easeOut",
      },
    },
  };

  const menuItems = [
    {
      id: 1,
      name: "کـــــــــــــەباب",
      color: "isolator-red",
    },
    {
      id: 2,
      name: "تکەی گۆشـت",
      color: "isolator-blue",
    },
    {
      id: 3,
      name: "جـــــــــــــــگەر",
      color: "isolator-green",
    },
    {
      id: 4,
      name: "تکەی مریشک",
      color: "isolator-red",
    },
    {
      id: 5,
      name: "بـــــــــــــــــــاڵ",
      color: "isolator-blue",
    },
  ];

  return (
    <section
      id="menu"
      className="py-20"
      style={{ backgroundColor: "#EEECE8" }}
      ref={ref}
    >
      <div className="container mx-auto px-4">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
          className="max-w-4xl mx-auto"
        >
          {/* Decorative Star */}
          <motion.div variants={itemVariants} className="text-center mb-12">
            <div className="text-4xl text-isolator-red mb-8">✦</div>
          </motion.div>

          {/* Menu Items */}
          <div className="space-y-8">
            {menuItems.map((item, index) => (
              <motion.div
                key={item.id}
                variants={itemVariants}
                className="text-center"
              >
                <div
                  className={`text-3xl md:text-4xl font-bold kurdish-text text-${item.color} mb-4`}
                >
                  {item.name}
                </div>
                {index < menuItems.length - 1 && (
                  <div className="w-32 h-px bg-gray-400 mx-auto mt-8"></div>
                )}
              </motion.div>
            ))}
          </div>

          {/* Decorative Star */}
          <motion.div variants={itemVariants} className="text-center mt-12">
            <div className="text-4xl text-isolator-blue">✦</div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
};

export default Menu;
