'use client';

import { motion } from 'framer-motion';
import { useInView } from 'framer-motion';
import { useRef } from 'react';

const Menu = () => {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: "-100px" });

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: "easeOut",
      },
    },
  };

  const menuItems = [
    {
      id: 1,
      name: "کەبابی بەرە",
      nameEn: "Lamb Kabab",
      description: "کەبابی بەرەی تازە بە ڕێگای نەریتی ئامادەکراو",
      descriptionEn: "Fresh lamb kabab prepared traditionally",
      price: "15,000",
      emoji: "🍖",
      popular: true
    },
    {
      id: 2,
      name: "کەبابی گا",
      nameEn: "Beef Kabab",
      description: "کەبابی گای ناوەڕاست بە تامێکی بێ وێنە",
      descriptionEn: "Medium beef kabab with unique taste",
      price: "12,000",
      emoji: "🥩"
    },
    {
      id: 3,
      name: "کەبابی مریشک",
      nameEn: "Chicken Kabab",
      description: "کەبابی مریشکی نەرم و بە تامی خۆش",
      descriptionEn: "Tender chicken kabab with delicious taste",
      price: "10,000",
      emoji: "🍗"
    },
    {
      id: 4,
      name: "کەبابی تێکەڵ",
      nameEn: "Mixed Kabab",
      description: "تێکەڵەیەک لە هەموو جۆرەکانی کەباب",
      descriptionEn: "A mix of all kabab varieties",
      price: "18,000",
      emoji: "🍽️",
      popular: true
    },
    {
      id: 5,
      name: "کەبابی کوبیدە",
      nameEn: "Kubideh Kabab",
      description: "کەبابی کوبیدەی نەریتی بە ڕێگای تایبەت",
      descriptionEn: "Traditional kubideh kabab with special recipe",
      price: "8,000",
      emoji: "🌭"
    },
    {
      id: 6,
      name: "کەبابی تکە",
      nameEn: "Tikka Kabab",
      description: "پارچە گۆشتی گەورە بە ڕێگای تایبەت",
      descriptionEn: "Large meat pieces with special preparation",
      price: "20,000",
      emoji: "🥓"
    }
  ];

  return (
    <section id="menu" className="py-20 bg-gradient-to-br from-gray-50 to-amber-50" ref={ref}>
      <div className="container mx-auto px-4">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
          className="max-w-6xl mx-auto"
        >
          {/* Section Title */}
          <motion.div variants={itemVariants} className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-amber-900 mb-4 kurdish-text">
              مینوی ئێمە
            </h2>
            <p className="text-xl text-gray-600" dir="ltr">
              Our Menu
            </p>
            <div className="w-24 h-1 bg-gradient-to-r from-red-500 to-orange-500 mx-auto mt-4"></div>
            <p className="text-lg text-gray-600 mt-6 kurdish-text max-w-2xl mx-auto">
              باشترین کەبابەکان بە نرخی گونجاو و کوالیتی بەرز
            </p>
          </motion.div>

          {/* Menu Grid */}
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {menuItems.map((item, index) => (
              <motion.div
                key={item.id}
                variants={itemVariants}
                whileHover={{ scale: 1.05, y: -5 }}
                className="bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 overflow-hidden relative"
              >
                {/* Popular Badge */}
                {item.popular && (
                  <div className="absolute top-4 right-4 bg-gradient-to-r from-red-500 to-orange-500 text-white px-3 py-1 rounded-full text-sm font-semibold kurdish-text z-10">
                    گەرم
                  </div>
                )}

                <div className="p-6">
                  {/* Emoji Icon */}
                  <div className="text-6xl mb-4 text-center">
                    {item.emoji}
                  </div>

                  {/* Item Name */}
                  <h3 className="text-2xl font-bold text-amber-900 mb-2 text-center kurdish-text">
                    {item.name}
                  </h3>
                  <p className="text-lg text-gray-600 text-center mb-4" dir="ltr">
                    {item.nameEn}
                  </p>

                  {/* Description */}
                  <p className="text-gray-700 mb-4 text-center kurdish-text leading-relaxed">
                    {item.description}
                  </p>
                  <p className="text-gray-600 text-sm text-center mb-6" dir="ltr">
                    {item.descriptionEn}
                  </p>

                  {/* Price */}
                  <div className="text-center">
                    <span className="text-3xl font-bold text-red-600">
                      {item.price}
                    </span>
                    <span className="text-lg text-gray-600 mr-2 kurdish-text">
                      دینار
                    </span>
                  </div>

                  {/* Order Button */}
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    className="w-full mt-6 bg-gradient-to-r from-red-500 to-orange-500 text-white py-3 rounded-full font-semibold hover:shadow-lg transition-all duration-300 kurdish-text"
                  >
                    داواکردن
                  </motion.button>
                </div>
              </motion.div>
            ))}
          </div>

          {/* Additional Info */}
          <motion.div
            variants={itemVariants}
            className="mt-16 text-center bg-gradient-to-r from-amber-100 to-orange-100 p-8 rounded-2xl"
          >
            <h3 className="text-2xl font-bold text-amber-900 mb-4 kurdish-text">
              زانیاری زیاتر
            </h3>
            <div className="grid md:grid-cols-3 gap-6 text-gray-700">
              <div className="kurdish-text">
                <div className="text-lg font-semibold mb-2">🕐 کاتی کارکردن</div>
                <p>هەموو ڕۆژێک لە ١٠:٠٠ بۆ ١١:٠٠ شەو</p>
              </div>
              <div className="kurdish-text">
                <div className="text-lg font-semibold mb-2">🚚 گەیاندن</div>
                <p>گەیاندنی خێرا بۆ هەموو ناوچەکانی هەولێر</p>
              </div>
              <div className="kurdish-text">
                <div className="text-lg font-semibold mb-2">💳 شێوازی پارەدان</div>
                <p>کاش، کارتی بانکی، زاد پەی</p>
              </div>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
};

export default Menu;
