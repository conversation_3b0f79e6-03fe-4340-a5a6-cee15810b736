'use client';

import { motion } from 'framer-motion';
import { useInView } from 'framer-motion';
import { useRef } from 'react';

const About = () => {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: "-100px" });

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.3,
        delayChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.8,
        ease: "easeOut",
      },
    },
  };

  return (
    <section id="about" className="py-20 bg-gradient-to-br from-amber-50 to-orange-50" ref={ref}>
      <div className="container mx-auto px-4">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
          className="max-w-6xl mx-auto"
        >
          {/* Section Title */}
          <motion.div variants={itemVariants} className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-amber-900 mb-4 kurdish-text">
              دەربارەی ئێمە
            </h2>
            <p className="text-xl text-gray-600" dir="ltr">
              About Us
            </p>
            <div className="w-24 h-1 bg-gradient-to-r from-red-500 to-orange-500 mx-auto mt-4"></div>
          </motion.div>

          <div className="grid lg:grid-cols-2 gap-12 items-center">
            {/* Story Content */}
            <motion.div variants={itemVariants} className="space-y-6">
              <h3 className="text-2xl md:text-3xl font-semibold text-amber-800 mb-6 kurdish-text">
                چیرۆکی ئێمە
              </h3>
              
              <div className="space-y-4 text-lg text-gray-700 leading-relaxed">
                <p className="kurdish-text">
                  لە ساڵی ١٩٨٥ەوە، هیوا مەدا کەبابچی یەکێک لە باشترین چێشتخانەکانی هەولێرە بۆ کەبابی کوردی. 
                  ئێمە شانازی بەوە دەکەین کە تامی ڕەسەنی کەبابی کوردی بە باشترین جۆر پێشکەش دەکەین.
                </p>
                
                <p className="kurdish-text">
                  هەموو کەبابەکانمان بە گۆشتی تازە و ڕەسەن و بە ڕێگای نەریتی ئامادە دەکرێن. 
                  ئێمە بە کەڵک وەرگرتن لە تەکنیکی کۆن و ڕێگای باپیرانمان، تامێکی بێ وێنە دروست دەکەین.
                </p>

                <div className="bg-white p-6 rounded-lg shadow-md border-l-4 border-red-500">
                  <p className="text-gray-800 italic kurdish-text">
                    "تامی ڕاستەقینەی کەبابی کوردی لە دڵی هەولێر"
                  </p>
                  <p className="text-gray-600 mt-2" dir="ltr">
                    "Authentic Kurdish Kabab Taste in the Heart of Erbil"
                  </p>
                </div>
              </div>
            </motion.div>

            {/* Features Grid */}
            <motion.div variants={itemVariants} className="grid grid-cols-1 sm:grid-cols-2 gap-6">
              <motion.div
                whileHover={{ scale: 1.05 }}
                className="bg-white p-6 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
              >
                <div className="text-4xl mb-4">🔥</div>
                <h4 className="text-xl font-semibold text-amber-800 mb-2 kurdish-text">
                  ئاگری نەریتی
                </h4>
                <p className="text-gray-600 kurdish-text">
                  کەبابەکانمان بە ئاگری دار و خەڵووز ئامادە دەکرێن
                </p>
              </motion.div>

              <motion.div
                whileHover={{ scale: 1.05 }}
                className="bg-white p-6 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
              >
                <div className="text-4xl mb-4">🥩</div>
                <h4 className="text-xl font-semibold text-amber-800 mb-2 kurdish-text">
                  گۆشتی تازە
                </h4>
                <p className="text-gray-600 kurdish-text">
                  هەموو ڕۆژێک گۆشتی تازە و باش هەڵدەبژێرین
                </p>
              </motion.div>

              <motion.div
                whileHover={{ scale: 1.05 }}
                className="bg-white p-6 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
              >
                <div className="text-4xl mb-4">👨‍🍳</div>
                <h4 className="text-xl font-semibold text-amber-800 mb-2 kurdish-text">
                  ئەزموونی زۆر
                </h4>
                <p className="text-gray-600 kurdish-text">
                  زیاتر لە ٣٨ ساڵ ئەزموون لە ئامادەکردنی کەباب
                </p>
              </motion.div>

              <motion.div
                whileHover={{ scale: 1.05 }}
                className="bg-white p-6 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
              >
                <div className="text-4xl mb-4">⭐</div>
                <h4 className="text-xl font-semibold text-amber-800 mb-2 kurdish-text">
                  کوالیتی بەرز
                </h4>
                <p className="text-gray-600 kurdish-text">
                  بەرزترین ئاستی کوالیتی لە هەموو شتێکدا
                </p>
              </motion.div>
            </motion.div>
          </div>

          {/* Statistics */}
          <motion.div
            variants={itemVariants}
            className="mt-16 grid grid-cols-2 md:grid-cols-4 gap-8 text-center"
          >
            <div className="bg-gradient-to-br from-red-500 to-orange-500 text-white p-6 rounded-xl">
              <div className="text-3xl font-bold mb-2">38+</div>
              <div className="text-sm kurdish-text">ساڵ ئەزموون</div>
            </div>
            <div className="bg-gradient-to-br from-amber-500 to-yellow-500 text-white p-6 rounded-xl">
              <div className="text-3xl font-bold mb-2">1000+</div>
              <div className="text-sm kurdish-text">کڕیاری دڵخۆش</div>
            </div>
            <div className="bg-gradient-to-br from-green-500 to-emerald-500 text-white p-6 rounded-xl">
              <div className="text-3xl font-bold mb-2">15+</div>
              <div className="text-sm kurdish-text">جۆری کەباب</div>
            </div>
            <div className="bg-gradient-to-br from-purple-500 to-pink-500 text-white p-6 rounded-xl">
              <div className="text-3xl font-bold mb-2">100%</div>
              <div className="text-sm kurdish-text">گۆشتی حەڵاڵ</div>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
};

export default About;
