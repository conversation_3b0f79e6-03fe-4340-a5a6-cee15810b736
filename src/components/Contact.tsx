"use client";

import { motion } from "framer-motion";
import { useInView } from "framer-motion";
import { useRef } from "react";

const Contact = () => {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: "-100px" });

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: "easeOut",
      },
    },
  };

  return (
    <section
      id="contact"
      className="py-20 bg-gradient-to-br from-amber-900 to-red-900 text-white"
      ref={ref}
    >
      <div className="container mx-auto px-4">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
          className="max-w-6xl mx-auto"
        >
          {/* Section Title */}
          <motion.div variants={itemVariants} className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold mb-4 kurdish-text">
              پەیوەندی
            </h2>
            <p className="text-xl text-amber-200" dir="ltr">
              Contact Us
            </p>
            <div className="w-24 h-1 bg-gradient-to-r from-yellow-400 to-orange-400 mx-auto mt-4"></div>
          </motion.div>

          <div className="grid lg:grid-cols-2 gap-12">
            {/* Contact Information */}
            <motion.div variants={itemVariants} className="space-y-8">
              <h3 className="text-2xl md:text-3xl font-semibold mb-8 kurdish-text">
                زانیاری پەیوەندی
              </h3>

              {/* Address */}
              <motion.div
                whileHover={{ scale: 1.02 }}
                className="bg-white/10 backdrop-blur-sm p-6 rounded-xl border border-white/20"
              >
                <div className="flex items-start space-x-4 space-x-reverse">
                  <div className="text-3xl">📍</div>
                  <div>
                    <h4 className="text-xl font-semibold mb-2 kurdish-text">
                      ناونیشان
                    </h4>
                    <p className="text-amber-200 kurdish-text leading-relaxed">
                      سلێمانی، ناوەندی شار، شەقامی ٦٠ مەتری
                      <br />
                      نزیک پاڵەوگای سەرەکی
                    </p>
                    <p className="text-amber-300 mt-2" dir="ltr">
                      Sulaymaniyah, City Center, 60m Street
                      <br />
                      Near Main Hospital
                    </p>
                  </div>
                </div>
              </motion.div>

              {/* Phone */}
              <motion.div
                whileHover={{ scale: 1.02 }}
                className="bg-white/10 backdrop-blur-sm p-6 rounded-xl border border-white/20"
              >
                <div className="flex items-start space-x-4 space-x-reverse">
                  <div className="text-3xl">📞</div>
                  <div>
                    <h4 className="text-xl font-semibold mb-2 kurdish-text">
                      تەلەفۆن
                    </h4>
                    <p className="text-amber-200 text-lg" dir="ltr">
                      +964 ************
                    </p>
                    <p className="text-amber-200 text-lg" dir="ltr">
                      +964 ************
                    </p>
                  </div>
                </div>
              </motion.div>

              {/* Hours */}
              <motion.div
                whileHover={{ scale: 1.02 }}
                className="bg-white/10 backdrop-blur-sm p-6 rounded-xl border border-white/20"
              >
                <div className="flex items-start space-x-4 space-x-reverse">
                  <div className="text-3xl">🕐</div>
                  <div>
                    <h4 className="text-xl font-semibold mb-2 kurdish-text">
                      کاتی کارکردن
                    </h4>
                    <div className="space-y-2 text-amber-200">
                      <p className="kurdish-text">
                        یەکشەممە تا پێنجشەممە: ١٠:٠٠ - ١١:٠٠
                      </p>
                      <p className="kurdish-text">
                        هەینی و شەممە: ١٠:٠٠ - ١٢:٠٠
                      </p>
                      <p className="text-amber-300 mt-2" dir="ltr">
                        Sun-Thu: 10:00 AM - 11:00 PM
                        <br />
                        Fri-Sat: 10:00 AM - 12:00 AM
                      </p>
                    </div>
                  </div>
                </div>
              </motion.div>

              {/* Social Media */}
              <motion.div
                whileHover={{ scale: 1.02 }}
                className="bg-white/10 backdrop-blur-sm p-6 rounded-xl border border-white/20"
              >
                <div className="flex items-start space-x-4 space-x-reverse">
                  <div className="text-3xl">📱</div>
                  <div>
                    <h4 className="text-xl font-semibold mb-4 kurdish-text">
                      میدیای کۆمەڵایەتی
                    </h4>
                    <div className="flex space-x-4 space-x-reverse">
                      <motion.a
                        whileHover={{ scale: 1.1 }}
                        whileTap={{ scale: 0.9 }}
                        href="#"
                        className="bg-blue-600 p-3 rounded-full hover:bg-blue-700 transition-colors"
                      >
                        <span className="text-xl">📘</span>
                      </motion.a>
                      <motion.a
                        whileHover={{ scale: 1.1 }}
                        whileTap={{ scale: 0.9 }}
                        href="#"
                        className="bg-pink-600 p-3 rounded-full hover:bg-pink-700 transition-colors"
                      >
                        <span className="text-xl">📷</span>
                      </motion.a>
                      <motion.a
                        whileHover={{ scale: 1.1 }}
                        whileTap={{ scale: 0.9 }}
                        href="#"
                        className="bg-green-600 p-3 rounded-full hover:bg-green-700 transition-colors"
                      >
                        <span className="text-xl">💬</span>
                      </motion.a>
                    </div>
                  </div>
                </div>
              </motion.div>
            </motion.div>

            {/* Map/Location */}
            <motion.div variants={itemVariants} className="space-y-8">
              <h3 className="text-2xl md:text-3xl font-semibold mb-8 kurdish-text">
                شوێنی ئێمە
              </h3>

              {/* Map Placeholder */}
              <motion.div
                whileHover={{ scale: 1.02 }}
                className="bg-white/10 backdrop-blur-sm p-8 rounded-xl border border-white/20 h-64 flex items-center justify-center"
              >
                <div className="text-center">
                  <div className="text-6xl mb-4">🗺️</div>
                  <p className="text-amber-200 kurdish-text">نەخشەی شوێن</p>
                  <p className="text-amber-300" dir="ltr">
                    Location Map
                  </p>
                </div>
              </motion.div>

              {/* Quick Actions */}
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className="bg-green-600 hover:bg-green-700 text-white p-4 rounded-xl font-semibold transition-all duration-300 kurdish-text"
                >
                  پەیوەندی تەلەفۆنی
                </motion.button>

                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className="bg-blue-600 hover:bg-blue-700 text-white p-4 rounded-xl font-semibold transition-all duration-300 kurdish-text"
                >
                  بینینی لە نەخشە
                </motion.button>
              </div>

              {/* Delivery Info */}
              <motion.div
                whileHover={{ scale: 1.02 }}
                className="bg-gradient-to-r from-green-600/20 to-emerald-600/20 backdrop-blur-sm p-6 rounded-xl border border-green-400/30"
              >
                <h4 className="text-xl font-semibold mb-4 kurdish-text flex items-center">
                  <span className="text-2xl ml-2">🚚</span>
                  گەیاندنی خێرا
                </h4>
                <p className="text-amber-200 kurdish-text mb-2">
                  گەیاندن بۆ هەموو ناوچەکانی سلێمانی
                </p>
                <p className="text-amber-300" dir="ltr">
                  Fast delivery to all areas of Sulaymaniyah
                </p>
                <div className="mt-4 text-sm text-amber-200 kurdish-text">
                  <p>• کەمترین داواکاری: ٢٠,٠٠٠ دینار</p>
                  <p>• کاتی گەیاندن: ٣٠-٤٥ خولەک</p>
                  <p>• نرخی گەیاندن: ٢,٠٠٠ دینار</p>
                </div>
              </motion.div>
            </motion.div>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default Contact;
