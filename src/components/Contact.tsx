"use client";

import { motion } from "framer-motion";
import { useInView } from "framer-motion";
import { useRef } from "react";

const Contact = () => {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: "-100px" });

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: "easeOut",
      },
    },
  };

  return (
    <section
      id="contact"
      className="py-20 text-gray-800"
      style={{ backgroundColor: "#EEECE8" }}
      ref={ref}
    >
      <div className="container mx-auto px-4">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
          className="max-w-4xl mx-auto"
        >
          {/* Decorative Star */}
          <motion.div variants={itemVariants} className="text-center mb-12">
            <div className="text-4xl text-isolator-blue mb-8">✦</div>
          </motion.div>

          {/* Contact Information */}
          <div className="max-w-2xl mx-auto text-center space-y-8">
            {/* Phone Numbers */}
            <motion.div variants={itemVariants} className="space-y-4">
              <div className="text-lg kurdish-text">
                سلێمانی سەرووی قاقەلمێوت
              </div>
              <div className="text-2xl font-bold" dir="ltr">
                0770 154 0509
              </div>
              <div className="text-lg kurdish-text">
                کارگێڕی بەرپرسی خامەسەخانەی جمهوری
              </div>
              <div className="text-2xl font-bold" dir="ltr">
                0773 206 1936
              </div>
              <div className="text-lg kurdish-text">کوپانیان هیوە</div>
            </motion.div>

            {/* Decorative Star */}
            <motion.div variants={itemVariants} className="text-center">
              <div className="text-4xl text-isolator-green">✦</div>
            </motion.div>

            {/* Social Media Icons */}
            <motion.div
              variants={itemVariants}
              className="flex justify-center space-x-6 space-x-reverse mt-12"
            >
              <motion.a
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                href="#"
                className="w-12 h-12 bg-isolator-green rounded-full flex items-center justify-center text-white text-xl hover:shadow-lg transition-all duration-300"
              >
                <span>📱</span>
              </motion.a>
              <motion.a
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                href="#"
                className="w-12 h-12 bg-isolator-blue rounded-full flex items-center justify-center text-white text-xl hover:shadow-lg transition-all duration-300"
              >
                <span>📘</span>
              </motion.a>
              <motion.a
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                href="#"
                className="w-12 h-12 bg-isolator-red rounded-full flex items-center justify-center text-white text-xl hover:shadow-lg transition-all duration-300"
              >
                <span>📷</span>
              </motion.a>
              <motion.a
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                href="#"
                className="w-12 h-12 bg-gray-800 rounded-full flex items-center justify-center text-white text-xl hover:shadow-lg transition-all duration-300"
              >
                <span>🎵</span>
              </motion.a>
            </motion.div>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default Contact;
