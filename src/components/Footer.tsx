"use client";

import { motion } from "framer-motion";

const Footer = () => {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="bg-gradient-to-br from-gray-900 to-black text-white">
      {/* Main Footer Content */}
      <div className="container mx-auto px-4 py-12">
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Restaurant Info */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="space-y-4"
          >
            <h3 className="text-2xl font-bold text-amber-400 kurdish-text">
              هیوا مەدا کەبابچی
            </h3>
            <p className="text-gray-300 kurdish-text leading-relaxed">
              تامی ڕەسەنی کەبابی کوردی لە دڵی سلێمانی. ئێمە شانازی بەوە دەکەین
              کە باشترین کەباب پێشکەش دەکەین.
            </p>
            <p className="text-gray-400 text-sm" dir="ltr">
              Authentic Kurdish Kabab in the Heart of Sulaymaniyah
            </p>
          </motion.div>

          {/* Quick Links */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
            className="space-y-4"
          >
            <h4 className="text-xl font-semibold text-amber-400 kurdish-text">
              بەستەرە خێراکان
            </h4>
            <ul className="space-y-2">
              <li>
                <motion.a
                  whileHover={{ x: 5 }}
                  href="#about"
                  className="text-gray-300 hover:text-amber-400 transition-colors kurdish-text"
                >
                  دەربارەی ئێمە
                </motion.a>
              </li>
              <li>
                <motion.a
                  whileHover={{ x: 5 }}
                  href="#menu"
                  className="text-gray-300 hover:text-amber-400 transition-colors kurdish-text"
                >
                  مینو
                </motion.a>
              </li>
              <li>
                <motion.a
                  whileHover={{ x: 5 }}
                  href="#contact"
                  className="text-gray-300 hover:text-amber-400 transition-colors kurdish-text"
                >
                  پەیوەندی
                </motion.a>
              </li>
              <li>
                <motion.a
                  whileHover={{ x: 5 }}
                  href="#"
                  className="text-gray-300 hover:text-amber-400 transition-colors kurdish-text"
                >
                  گەیاندن
                </motion.a>
              </li>
            </ul>
          </motion.div>

          {/* Contact Info */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="space-y-4"
          >
            <h4 className="text-xl font-semibold text-amber-400 kurdish-text">
              پەیوەندی
            </h4>
            <div className="space-y-3">
              <div className="flex items-center space-x-3 space-x-reverse">
                <span className="text-amber-400">📍</span>
                <span className="text-gray-300 text-sm kurdish-text">
                  سلێمانی، شەقامی ٦٠ مەتری
                </span>
              </div>
              <div className="flex items-center space-x-3 space-x-reverse">
                <span className="text-amber-400">📞</span>
                <span className="text-gray-300 text-sm" dir="ltr">
                  +964 750 123 4567
                </span>
              </div>
              <div className="flex items-center space-x-3 space-x-reverse">
                <span className="text-amber-400">🕐</span>
                <span className="text-gray-300 text-sm kurdish-text">
                  ١٠:٠٠ - ١١:٠٠ هەموو ڕۆژێک
                </span>
              </div>
            </div>
          </motion.div>

          {/* Social Media & Newsletter */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
            className="space-y-4"
          >
            <h4 className="text-xl font-semibold text-amber-400 kurdish-text">
              میدیای کۆمەڵایەتی
            </h4>

            {/* Social Links */}
            <div className="flex space-x-4 space-x-reverse">
              <motion.a
                whileHover={{ scale: 1.2, rotate: 5 }}
                whileTap={{ scale: 0.9 }}
                href="#"
                className="bg-blue-600 p-3 rounded-full hover:bg-blue-700 transition-colors"
              >
                <span className="text-xl">📘</span>
              </motion.a>
              <motion.a
                whileHover={{ scale: 1.2, rotate: -5 }}
                whileTap={{ scale: 0.9 }}
                href="#"
                className="bg-pink-600 p-3 rounded-full hover:bg-pink-700 transition-colors"
              >
                <span className="text-xl">📷</span>
              </motion.a>
              <motion.a
                whileHover={{ scale: 1.2, rotate: 5 }}
                whileTap={{ scale: 0.9 }}
                href="#"
                className="bg-green-600 p-3 rounded-full hover:bg-green-700 transition-colors"
              >
                <span className="text-xl">💬</span>
              </motion.a>
            </div>

            {/* Newsletter */}
            <div className="mt-6">
              <p className="text-gray-300 text-sm mb-3 kurdish-text">
                بۆ زانینی ئۆفەرە تایبەتەکان
              </p>
              <div className="flex">
                <input
                  type="email"
                  placeholder="ئیمەیڵەکەت"
                  className="flex-1 px-3 py-2 bg-gray-800 text-white rounded-r-lg border border-gray-700 focus:border-amber-400 focus:outline-none text-sm"
                  dir="rtl"
                />
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className="bg-amber-600 hover:bg-amber-700 px-4 py-2 rounded-l-lg transition-colors"
                >
                  <span className="text-sm">📧</span>
                </motion.button>
              </div>
            </div>
          </motion.div>
        </div>
      </div>

      {/* Bottom Bar */}
      <div className="border-t border-gray-800">
        <div className="container mx-auto px-4 py-6">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            {/* Copyright */}
            <motion.div
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              transition={{ duration: 0.6 }}
              className="text-gray-400 text-sm text-center md:text-right"
            >
              <p className="mt-1" dir="ltr">
                ©2025 Hiwa Madhat Kababchi™ (HMK). All rights reserved. Co-owned and managed by Ahmad Hiwa Madhat.
              </p>
            </motion.div>

            {/* Additional Links */}
            <motion.div
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              transition={{ duration: 0.6, delay: 0.1 }}
              className="flex space-x-6 space-x-reverse text-sm"
            >
              <a
                href="#"
                className="text-gray-400 hover:text-amber-400 transition-colors kurdish-text"
              >
                ڕێساکان
              </a>
              <a
                href="#"
                className="text-gray-400 hover:text-amber-400 transition-colors kurdish-text"
              >
                نهێنیەتی
              </a>
              <a
                href="#"
                className="text-gray-400 hover:text-amber-400 transition-colors kurdish-text"
              >
                مەرجەکان
              </a>
            </motion.div>
          </div>
        </div>
      </div>

      {/* Floating Back to Top Button */}
      <motion.button
        initial={{ opacity: 0, scale: 0 }}
        whileInView={{ opacity: 1, scale: 1 }}
        whileHover={{ scale: 1.1 }}
        whileTap={{ scale: 0.9 }}
        onClick={() => window.scrollTo({ top: 0, behavior: "smooth" })}
        className="fixed bottom-8 left-8 bg-gradient-to-r from-amber-500 to-orange-500 text-white p-4 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 z-50"
      >
        <span className="text-xl">⬆️</span>
      </motion.button>
    </footer>
  );
};

export default Footer;
