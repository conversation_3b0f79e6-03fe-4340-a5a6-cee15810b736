@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-ibm-plex-sans-arabic);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-ibm-plex-sans-arabic), Arial, Helvetica, sans-serif;
}

/* Smooth scrolling for navigation */
html {
  scroll-behavior: smooth;
}

/* Custom styles for Kurdish text */
.kurdish-text {
  font-family: var(--font-ibm-plex-sans-arabic), Arial, Helvetica, sans-serif;
  direction: rtl;
  text-align: right;
}
