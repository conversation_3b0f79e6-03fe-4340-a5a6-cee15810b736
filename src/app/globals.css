@import "tailwindcss";

:root {
  --background: #eeece8;
  --foreground: #171717;
  --card-background: #e5e3df;
  --isolator-red: #c24343;
  --isolator-blue: #4398c2;
  --isolator-green: #449846;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-ibm-plex-sans-arabic);
  --color-card-bg: var(--card-background);
  --color-isolator-red: var(--isolator-red);
  --color-isolator-blue: var(--isolator-blue);
  --color-isolator-green: var(--isolator-green);
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-ibm-plex-sans-arabic), Arial, Helvetica, sans-serif;
}

/* Smooth scrolling for navigation */
html {
  scroll-behavior: smooth;
}

/* Custom styles for Kurdish text */
.kurdish-text {
  font-family: var(--font-ibm-plex-sans-arabic), Arial, Helvetica, sans-serif;
  direction: rtl;
  text-align: right;
}

/* Custom color classes */
.bg-card {
  background-color: var(--card-background);
}

.bg-isolator-red {
  background-color: var(--isolator-red);
}

.bg-isolator-blue {
  background-color: var(--isolator-blue);
}

.bg-isolator-green {
  background-color: var(--isolator-green);
}

.text-isolator-red {
  color: var(--isolator-red);
}

.text-isolator-blue {
  color: var(--isolator-blue);
}

.text-isolator-green {
  color: var(--isolator-green);
}
