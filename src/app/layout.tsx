import type { Metadata } from "next";
import { IBM_Plex_Sans_Arabic } from "next/font/google";
import "./globals.css";

const ibmPlexSansArabic = IBM_Plex_Sans_Arabic({
  variable: "--font-ibm-plex-sans-arabic",
  subsets: ["arabic"],
  weight: ["100", "200", "300", "400", "500", "600", "700"],
});

export const metadata: Metadata = {
  title: "هیوا مەدا کەبابچی - Hiwa <PERSON>ha <PERSON>ba<PERSON>chy",
  description:
    "چێشتخانەی کەبابی کوردی لە هەولێر - Kurdish Kabab Restaurant in Erbil",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="ku" dir="rtl">
      <body className={`${ibmPlexSansArabic.variable} antialiased font-sans`}>
        {children}
      </body>
    </html>
  );
}
