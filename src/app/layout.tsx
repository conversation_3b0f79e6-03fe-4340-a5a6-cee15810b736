import type { Metadata } from "next";
import { IBM_Plex_Sans_Arabic } from "next/font/google";
import "./globals.css";

const ibmPlexSansArabic = IBM_Plex_Sans_Arabic({
  variable: "--font-ibm-plex-sans-arabic",
  subsets: ["arabic"],
  weight: ["100", "200", "300", "400", "500", "600", "700"],
  display: "swap",
});

export const metadata: Metadata = {
  title: "هیوا مدحت کەبابچی - Hiwa Madhat Kababchi",
  description:
    "یەکەمین بڕاندی کەباب لە کوردستان و عێراق - First Kabab Brand in Kurdistan and Iraq",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="ckb" dir="rtl">
      <body className={`${ibmPlexSansArabic.variable} antialiased font-sans`}>
        {children}
      </body>
    </html>
  );
}
