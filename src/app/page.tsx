"use client";

export default function Home() {
  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="hero-gradient kabab-pattern min-h-screen flex items-center justify-center relative overflow-hidden">
        <div className="container mx-auto px-4 text-center text-white relative z-10">
          <div className="max-w-4xl mx-auto">
            <h1 className="text-5xl md:text-7xl font-bold mb-6 leading-tight">
              هیوا مەدا کەبابچی
            </h1>
            <p className="text-xl md:text-2xl mb-8 opacity-90">
              چێشتخانەی کەبابی کوردی لە هەولێر
            </p>
            <p className="text-lg md:text-xl mb-12 opacity-80">
              تامی ڕەسەنی کەبابی کوردی لە دڵی هەولێر
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="bg-white text-amber-800 px-8 py-4 rounded-full font-semibold text-lg hover:bg-amber-50 transition-colors shadow-lg">
                بینینی مینیو
              </button>
              <button className="border-2 border-white text-white px-8 py-4 rounded-full font-semibold text-lg hover:bg-white hover:text-amber-800 transition-colors">
                پەیوەندی
              </button>
            </div>
          </div>
        </div>

        {/* Decorative elements */}
        <div className="absolute top-20 left-10 w-20 h-20 bg-white/10 rounded-full" />
        <div className="absolute bottom-20 right-10 w-16 h-16 bg-white/10 rounded-full" />
      </section>

      {/* About Section */}
      <section className="py-20 bg-amber-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-amber-800 mb-6">
              دەربارەی ئێمە
            </h2>
            <p className="text-xl text-amber-700 max-w-3xl mx-auto">
              لە ساڵی ١٩٨٥ەوە، هیوا مەدا کەبابچی یەکێکە لە باشترین چێشتخانەکانی
              هەولێر
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-12 items-center">
            <div>
              <h3 className="text-3xl font-bold text-amber-800 mb-6">
                چیرۆکی ئێمە
              </h3>
              <p className="text-lg text-gray-700 mb-6 leading-relaxed">
                لە ماڵێکی بچووکەوە دەستمان پێکرد، ئێستا بووینە یەکێک لە
                ناسراوترین چێشتخانەکانی هەولێر. ئێمە شانازی بەوە دەکەین کە
                کەبابی کوردی بە شێوەیەکی ڕەسەن و بە باشترین جۆر دروست دەکەین.
              </p>
              <p className="text-lg text-gray-700 leading-relaxed">
                هەموو ڕۆژێک گۆشتی تازە و سەوزەی ڕەسەن بەکار دەهێنین بۆ ئەوەی
                باشترین تام بۆ میوانەکانمان دابین بکەین.
              </p>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="bg-white p-6 rounded-lg shadow-lg text-center">
                <div className="text-3xl font-bold text-amber-800 mb-2">
                  ٣٨+
                </div>
                <div className="text-gray-600">ساڵ ئەزموون</div>
              </div>
              <div className="bg-white p-6 rounded-lg shadow-lg text-center">
                <div className="text-3xl font-bold text-amber-800 mb-2">
                  ١٠٠٠+
                </div>
                <div className="text-gray-600">میوانی ڕۆژانە</div>
              </div>
              <div className="bg-white p-6 rounded-lg shadow-lg text-center">
                <div className="text-3xl font-bold text-amber-800 mb-2">
                  ٢٠+
                </div>
                <div className="text-gray-600">جۆری کەباب</div>
              </div>
              <div className="bg-white p-6 rounded-lg shadow-lg text-center">
                <div className="text-3xl font-bold text-amber-800 mb-2">
                  ٥⭐
                </div>
                <div className="text-gray-600">هەڵسەنگاندن</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Menu Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl md:text-5xl font-bold text-amber-800 mb-6">
              مینیوی ئێمە
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              باشترین کەبابەکان بە تامی ڕەسەنی کوردی
            </p>
          </motion.div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {/* Kabab Items */}
            <motion.div
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.1 }}
              viewport={{ once: true }}
              className="bg-amber-50 rounded-lg p-6 shadow-lg hover:shadow-xl transition-shadow"
            >
              <div className="text-center mb-4">
                <div className="w-20 h-20 bg-amber-200 rounded-full mx-auto mb-4 flex items-center justify-center text-3xl">
                  🍖
                </div>
                <h3 className="text-2xl font-bold text-amber-800 mb-2">
                  کەبابی گۆشت
                </h3>
                <p className="text-gray-600 mb-4">
                  کەبابی گۆشتی بەرانی تازە بە ئەسپەنجی کوردی
                </p>
                <div className="text-2xl font-bold text-amber-800">
                  ١٥,٠٠٠ د.ع
                </div>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              viewport={{ once: true }}
              className="bg-amber-50 rounded-lg p-6 shadow-lg hover:shadow-xl transition-shadow"
            >
              <div className="text-center mb-4">
                <div className="w-20 h-20 bg-amber-200 rounded-full mx-auto mb-4 flex items-center justify-center text-3xl">
                  🐔
                </div>
                <h3 className="text-2xl font-bold text-amber-800 mb-2">
                  کەبابی مریشک
                </h3>
                <p className="text-gray-600 mb-4">
                  کەبابی مریشکی تازە بە ئەسپەنجی تایبەت
                </p>
                <div className="text-2xl font-bold text-amber-800">
                  ١٢,٠٠٠ د.ع
                </div>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.3 }}
              viewport={{ once: true }}
              className="bg-amber-50 rounded-lg p-6 shadow-lg hover:shadow-xl transition-shadow"
            >
              <div className="text-center mb-4">
                <div className="w-20 h-20 bg-amber-200 rounded-full mx-auto mb-4 flex items-center justify-center text-3xl">
                  🥩
                </div>
                <h3 className="text-2xl font-bold text-amber-800 mb-2">
                  کەبابی تکە
                </h3>
                <p className="text-gray-600 mb-4">
                  پارچە گۆشتی گەورە بە ئەسپەنجی کوردی
                </p>
                <div className="text-2xl font-bold text-amber-800">
                  ١٨,٠٠٠ د.ع
                </div>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
              viewport={{ once: true }}
              className="bg-amber-50 rounded-lg p-6 shadow-lg hover:shadow-xl transition-shadow"
            >
              <div className="text-center mb-4">
                <div className="w-20 h-20 bg-amber-200 rounded-full mx-auto mb-4 flex items-center justify-center text-3xl">
                  🍽️
                </div>
                <h3 className="text-2xl font-bold text-amber-800 mb-2">
                  کەبابی تایبەت
                </h3>
                <p className="text-gray-600 mb-4">
                  تێکەڵەی گۆشت و مریشک بە ئەسپەنجی تایبەت
                </p>
                <div className="text-2xl font-bold text-amber-800">
                  ٢٠,٠٠٠ د.ع
                </div>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.5 }}
              viewport={{ once: true }}
              className="bg-amber-50 rounded-lg p-6 shadow-lg hover:shadow-xl transition-shadow"
            >
              <div className="text-center mb-4">
                <div className="w-20 h-20 bg-amber-200 rounded-full mx-auto mb-4 flex items-center justify-center text-3xl">
                  🥙
                </div>
                <h3 className="text-2xl font-bold text-amber-800 mb-2">
                  کەبابی نان
                </h3>
                <p className="text-gray-600 mb-4">
                  کەبابی گۆشت لەگەڵ نانی کوردی
                </p>
                <div className="text-2xl font-bold text-amber-800">
                  ١٠,٠٠٠ د.ع
                </div>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.6 }}
              viewport={{ once: true }}
              className="bg-amber-50 rounded-lg p-6 shadow-lg hover:shadow-xl transition-shadow"
            >
              <div className="text-center mb-4">
                <div className="w-20 h-20 bg-amber-200 rounded-full mx-auto mb-4 flex items-center justify-center text-3xl">
                  🍚
                </div>
                <h3 className="text-2xl font-bold text-amber-800 mb-2">
                  کەبابی برنج
                </h3>
                <p className="text-gray-600 mb-4">
                  کەبابی گۆشت لەگەڵ برنجی زەرد
                </p>
                <div className="text-2xl font-bold text-amber-800">
                  ١٣,٠٠٠ د.ع
                </div>
              </div>
            </motion.div>
          </div>

          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.7 }}
            viewport={{ once: true }}
            className="text-center mt-12"
          >
            <button className="bg-amber-800 text-white px-8 py-4 rounded-full font-semibold text-lg hover:bg-amber-700 transition-colors shadow-lg">
              بینینی مینیوی تەواو
            </button>
          </motion.div>
        </div>
      </section>

      {/* Contact Section */}
      <section className="py-20 bg-amber-800 text-white">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl md:text-5xl font-bold mb-6">
              پەیوەندی لەگەڵ ئێمە
            </h2>
            <p className="text-xl opacity-90 max-w-3xl mx-auto">
              بۆ دانانی مێز یان زانیاری زیاتر پەیوەندیمان پێوە بکەن
            </p>
          </motion.div>

          <div className="grid md:grid-cols-3 gap-8">
            <motion.div
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.1 }}
              viewport={{ once: true }}
              className="text-center"
            >
              <div className="w-16 h-16 bg-white/20 rounded-full mx-auto mb-4 flex items-center justify-center text-2xl">
                📍
              </div>
              <h3 className="text-2xl font-bold mb-4">ناونیشان</h3>
              <p className="text-lg opacity-90">
                شەقامی ٦٠ مەتری، نزیک پاڕکی شار
                <br />
                هەولێر، هەرێمی کوردستان
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              viewport={{ once: true }}
              className="text-center"
            >
              <div className="w-16 h-16 bg-white/20 rounded-full mx-auto mb-4 flex items-center justify-center text-2xl">
                📞
              </div>
              <h3 className="text-2xl font-bold mb-4">تەلەفۆن</h3>
              <p className="text-lg opacity-90">
                +964 750 123 4567
                <br />
                +964 751 987 6543
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.3 }}
              viewport={{ once: true }}
              className="text-center"
            >
              <div className="w-16 h-16 bg-white/20 rounded-full mx-auto mb-4 flex items-center justify-center text-2xl">
                🕐
              </div>
              <h3 className="text-2xl font-bold mb-4">کاتی کارکردن</h3>
              <p className="text-lg opacity-90">
                ڕۆژانە: ١٠:٠٠ - ١٢:٠٠ شەو
                <br />
                هەفتە ٧ ڕۆژ کراوەیە
              </p>
            </motion.div>
          </div>

          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            viewport={{ once: true }}
            className="text-center mt-12"
          >
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="bg-white text-amber-800 px-8 py-4 rounded-full font-semibold text-lg hover:bg-amber-50 transition-colors shadow-lg">
                پەیوەندی تەلەفۆنی
              </button>
              <button className="border-2 border-white text-white px-8 py-4 rounded-full font-semibold text-lg hover:bg-white hover:text-amber-800 transition-colors">
                بینینی نەخشە
              </button>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-amber-900 text-white py-12">
        <div className="container mx-auto px-4">
          <div className="grid md:grid-cols-3 gap-8">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
            >
              <h3 className="text-2xl font-bold mb-4">هیوا مەدا کەبابچی</h3>
              <p className="text-amber-200 mb-4">
                لە ساڵی ١٩٨٥ەوە خزمەتی باشترین کەبابی کوردی دەکەین
              </p>
              <div className="flex space-x-4 space-x-reverse">
                <div className="w-10 h-10 bg-amber-700 rounded-full flex items-center justify-center cursor-pointer hover:bg-amber-600 transition-colors">
                  📘
                </div>
                <div className="w-10 h-10 bg-amber-700 rounded-full flex items-center justify-center cursor-pointer hover:bg-amber-600 transition-colors">
                  📷
                </div>
                <div className="w-10 h-10 bg-amber-700 rounded-full flex items-center justify-center cursor-pointer hover:bg-amber-600 transition-colors">
                  🐦
                </div>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.1 }}
              viewport={{ once: true }}
            >
              <h3 className="text-xl font-bold mb-4">بەشەکان</h3>
              <ul className="space-y-2 text-amber-200">
                <li>
                  <a href="#" className="hover:text-white transition-colors">
                    سەرەتا
                  </a>
                </li>
                <li>
                  <a href="#" className="hover:text-white transition-colors">
                    دەربارەی ئێمە
                  </a>
                </li>
                <li>
                  <a href="#" className="hover:text-white transition-colors">
                    مینیو
                  </a>
                </li>
                <li>
                  <a href="#" className="hover:text-white transition-colors">
                    پەیوەندی
                  </a>
                </li>
              </ul>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              viewport={{ once: true }}
            >
              <h3 className="text-xl font-bold mb-4">زانیاری پەیوەندی</h3>
              <div className="space-y-2 text-amber-200">
                <p>📍 شەقامی ٦٠ مەتری، هەولێر</p>
                <p>📞 +964 750 123 4567</p>
                <p>✉️ <EMAIL></p>
                <p>🕐 ١٠:٠٠ - ١٢:٠٠ شەو</p>
              </div>
            </motion.div>
          </div>

          <motion.div
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            transition={{ duration: 0.8, delay: 0.3 }}
            viewport={{ once: true }}
            className="border-t border-amber-700 mt-8 pt-8 text-center text-amber-200"
          >
            <p>&copy; ٢٠٢٤ هیوا مەدا کەبابچی. هەموو مافەکان پارێزراون.</p>
          </motion.div>
        </div>
      </footer>
    </div>
  );
}
